// ==UserScript==
// @name         Auto HDR Pro
// @namespace    http://tampermonkey.net/
// @version      5.4
// @description  High-performance HDR effect for video sites with advanced optimizations and a smooth live preview.
// <AUTHOR> (Optimized by Gemini)
// @icon         data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' width='64' height='64'><rect width='100%' height='100%' rx='12' fill='%23007bff'/><text x='50%' y='54%' font-size='28' font-family='Arial' font-weight='bold' fill='white' text-anchor='middle' dominant-baseline='middle'>HDR</text></svg>
// @match        *://*.youtube.com/*
// @match        *://*.netflix.com/*
// @match        *://*.twitch.tv/*
// @match        *://*.vimeo.com/*
// @match        *://*.dailymotion.com/*
// @match        *://*.hulu.com/*
// @match        *://*.primevideo.com/*
// @match        *://*.disneyplus.com/*
// @match        *://*.max.com/*
// @match        *://*.crunchyroll.com/*
// @match        *://*.tiktok.com/*
// @match        *://*.instagram.com/*
// @match        *://*.facebook.com/*
// @match        *://*.bilibili.tv/*
// @grant        GM_getValue
// @grant        GM_setValue
// @grant        GM_addStyle
// @run-at       document-end
// ==/UserScript==

(function() {
    'use strict';

    /** @type {Object<string, any>} */
    const CONSTANTS = {
        STORAGE_KEY: 'hdr_settings_v5',
        MUTATION_THROTTLE: 100,
        CLEANUP_INTERVAL: 30000,
        MAX_ELEMENTS_BATCH: 50,
        PERFORMANCE_SAMPLE_SIZE: 100,
        MIN_IMAGE_SIZE: 100,
        VIEWPORT_MARGIN: '50px'
    };

    /** @type {Object<string, any>} */
    const DEFAULT_SETTINGS = {
        enabled: true,
        brightness: 0.95,
        contrast: 1.05,
        saturation: 1.2,
        excludeSites: [],
        autoHideDelay: 3000,
        showOnHover: true,
        performanceMode: false,
        adaptiveThrottling: true,
        livePreview: true, // New setting for live preview control
        panelTop: 50,
        panelLeft: null,
        panelRight: 10,
        buttonTop: 10,
        buttonLeft: null,
        buttonRight: 10
    };

    /**
     * Monitors script performance metrics.
     */
    class PerformanceMonitor {
        constructor() {
            this.metrics = {
                domQueries: 0,
                filterApplications: 0,
                mutationCallbacks: 0,
                averageProcessingTime: 0,
                memoryUsage: 0
            };
            this._sampleSum = 0;
            this._samples = [];
            this.startTime = performance.now();
        }

        /**
         * Starts a performance measurement.
         * @param {string} operation - The name of the operation being measured.
         * @returns {{operation: string, startTime: number}}
         */
        startMeasure(operation) {
            return { operation, startTime: performance.now() };
        }

        /**
         * Ends a performance measurement and updates metrics.
         * @param {{operation: string, startTime: number}} measurement - The measurement object from startMeasure.
         */
        endMeasure(measurement) {
            const duration = performance.now() - measurement.startTime;
            this._samples.push(duration);
            this._sampleSum += duration;

            if (this._samples.length > CONSTANTS.PERFORMANCE_SAMPLE_SIZE) {
                this._sampleSum -= this._samples.shift();
            }

            this.metrics.averageProcessingTime = this._samples.length > 0 ? (this._sampleSum / this._samples.length) : 0;
            this.metrics[measurement.operation] = (this.metrics[measurement.operation] || 0) + 1;
        }

        /**
         * Retrieves the current performance metrics.
         * @returns {Object<string, any>}
         */
        getMetrics() {
            return {
                ...this.metrics,
                uptime: performance.now() - this.startTime,
                memoryUsage: performance.memory?.usedJSHeapSize ?? 0
            };
        }
    }

    /**
     * Manages the state and caching of DOM elements.
     */
    class ElementStateManager {
        constructor() {
            this.processedElements = new WeakSet();
            this.elementStates = new WeakMap();
            this.cachedElements = new Map();
            this.cacheValidityDuration = 5000; // 5 seconds
        }

        /** @param {Element} element */
        markProcessed(element) {
            this.processedElements.add(element);
        }

        /** @param {Element} element */
        getState(element) {
            return this.elementStates.get(element);
        }

        /**
         * @param {Element} element
         * @param {any} state
         */
        setState(element, state) {
            this.elementStates.set(element, state);
        }

        /**
         * Retrieves cached elements for a given selector or queries the DOM.
         * @param {string} selector - The CSS selector to query.
         * @returns {Element[]}
         */
        getCachedElements(selector) {
            const now = performance.now();
            if (this.cachedElements.has(selector)) {
                const { elements, timestamp } = this.cachedElements.get(selector);
                if ((now - timestamp) < this.cacheValidityDuration) {
                    return elements;
                }
            }

            const elements = Array.from(document.querySelectorAll(selector));
            this.cachedElements.set(selector, { elements, timestamp: now });
            return elements;
        }

        invalidateCache() {
            this.cachedElements.clear();
        }

        cleanup() {
            this.cachedElements.clear();
        }
    }

    /**
     * Processes and applies HDR effects to media elements.
     */
    class HDRProcessor {
        constructor(settings, stateManager, performanceMonitor) {
            this.settings = settings;
            this.stateManager = stateManager;
            this.performanceMonitor = performanceMonitor;
            this.intersectionObserver = null;
            this.pendingUpdates = new Set();
            this.updateScheduled = false;
            this.cssStyleElement = null;

            this.initializeIntersectionObserver();
            this.initializeCSSCustomProperties();
        }

        initializeIntersectionObserver() {
            if ('IntersectionObserver' in window) {
                this.intersectionObserver = new IntersectionObserver(
                    (entries) => this.handleIntersection(entries),
                    { rootMargin: CONSTANTS.VIEWPORT_MARGIN, threshold: 0.1 }
                );
            }
        }

        initializeCSSCustomProperties() {
            const style = document.createElement('style');
            style.textContent = `
                :root {
                    --hdr-brightness: ${this.settings.brightness};
                    --hdr-contrast: ${this.settings.contrast};
                    --hdr-saturation: ${this.settings.saturation};
                }
                .hdr-enhanced {
                    filter: brightness(var(--hdr-brightness))
                           contrast(var(--hdr-contrast))
                           saturate(var(--hdr-saturation)) !important;
                }`;
            document.head.appendChild(style);
            this.cssStyleElement = style;
        }

        updateCSSCustomProperties() {
            const root = document.documentElement;
            root.style.setProperty('--hdr-brightness', this.settings.brightness);
            root.style.setProperty('--hdr-contrast', this.settings.contrast);
            root.style.setProperty('--hdr-saturation', this.settings.saturation);
        }

        /** @param {IntersectionObserverEntry[]} entries */
        handleIntersection(entries) {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    this.pendingUpdates.add(entry.target);
                    this.scheduleUpdate();
                }
            });
        }

        scheduleUpdate() {
            if (this.updateScheduled) return;
            this.updateScheduled = true;
            const cb = () => {
                this.processPendingUpdates();
                this.updateScheduled = false;
            };
            (window.requestIdleCallback ?? requestAnimationFrame)(cb);
        }

        processPendingUpdates() {
            const measure = this.performanceMonitor.startMeasure('filterApplications');
            const elementsToProcess = Array.from(this.pendingUpdates).slice(0, CONSTANTS.MAX_ELEMENTS_BATCH);
            this.pendingUpdates.clear();

            elementsToProcess.forEach(element => this.applyHDRToElement(element));
            this.performanceMonitor.endMeasure(measure);
        }

        /** @param {Element} element */
        applyHDRToElement(element) {
            if (!this.settings.enabled || this.isSiteExcluded()) {
                this.removeHDRFromElement(element);
                return;
            }

            const newState = {
                brightness: this.settings.brightness,
                contrast: this.settings.contrast,
                saturation: this.settings.saturation
            };

            if (this.statesEqual(this.stateManager.getState(element), newState)) {
                return;
            }

            element.classList.add('hdr-enhanced');
            this.stateManager.setState(element, newState);
            this.stateManager.markProcessed(element);
        }

        /** @param {Element} element */
        removeHDRFromElement(element) {
            element.classList.remove('hdr-enhanced');
        }

        /**
         * @param {any} state1
         * @param {any} state2
         */
        statesEqual(state1, state2) {
            if (!state1 || !state2) return false;
            return state1.brightness === state2.brightness &&
                   state1.contrast === state2.contrast &&
                   state1.saturation === state2.saturation;
        }

        processMedia(selector) {
            const measure = this.performanceMonitor.startMeasure('domQueries');
            const elements = this.stateManager.getCachedElements(selector);
            this.performanceMonitor.endMeasure(measure);

            const validElements = (selector === 'img')
                ? elements.filter(img => img.naturalWidth > CONSTANTS.MIN_IMAGE_SIZE && img.naturalHeight > CONSTANTS.MIN_IMAGE_SIZE)
                : elements;

            validElements.forEach(el => {
                if (this.intersectionObserver) {
                    this.intersectionObserver.observe(el);
                } else {
                    this.pendingUpdates.add(el);
                }
            });

            if (!this.intersectionObserver) {
                this.scheduleUpdate();
            }
        }

        applyHDR() {
            this.updateCSSCustomProperties();
            this.processMedia('video');
            this.processMedia('img');
        }

        removeAllHDR() {
            document.querySelectorAll('.hdr-enhanced').forEach(el => this.removeHDRFromElement(el));
        }

        isSiteExcluded() {
            const currentHost = window.location.hostname.toLowerCase();
            return this.settings.excludeSites.some(site => currentHost.includes(site.toLowerCase()));
        }

        cleanup() {
            this.intersectionObserver?.disconnect();
            this.pendingUpdates.clear();
            this.cssStyleElement?.remove();
        }
    }

    /**
     * Observes DOM mutations to detect new media elements.
     */
    class OptimizedMutationObserver {
        constructor(hdrProcessor, stateManager, performanceMonitor) {
            this.hdrProcessor = hdrProcessor;
            this.stateManager = stateManager;
            this.performanceMonitor = performanceMonitor;
            this.observer = null;
            this.debouncedCallback = this.debounce(this.handleMutations.bind(this), CONSTANTS.MUTATION_THROTTLE);
        }

        debounce(func, delay) {
            let timeoutId;
            return (...args) => {
                clearTimeout(timeoutId);
                timeoutId = setTimeout(() => func.apply(this, args), delay);
            };
        }

        /** @param {MutationRecord[]} mutations */
        handleMutations(mutations) {
            const measure = this.performanceMonitor.startMeasure('mutationCallbacks');
            let needsUpdate = false;

            for (const mutation of mutations) {
                if (mutation.type !== 'childList') continue;

                for (const node of mutation.addedNodes) {
                    if (node.nodeType !== Node.ELEMENT_NODE) continue;

                    // Check if the node itself or its descendants are media elements
                    if (node.matches('video, img')) {
                        this.hdrProcessor.pendingUpdates.add(node);
                        needsUpdate = true;
                    }
                    node.querySelectorAll('video, img').forEach(media => {
                        this.hdrProcessor.pendingUpdates.add(media);
                        needsUpdate = true;
                    });
                }
            }

            if (needsUpdate) {
                this.stateManager.invalidateCache();
                this.hdrProcessor.scheduleUpdate();
            }

            this.performanceMonitor.endMeasure(measure);
        }

        start() {
            if (this.observer) return;
            this.observer = new MutationObserver(this.debouncedCallback);
            this.observer.observe(document.body, { childList: true, subtree: true });
        }

        stop() {
            this.observer?.disconnect();
            this.observer = null;
        }
    }

    /**
     * Manages memory by periodically cleaning up resources.
     */
    class MemoryManager {
        constructor(stateManager, hdrProcessor, mutationObserver) {
            this.stateManager = stateManager;
            this.hdrProcessor = hdrProcessor;
            this.mutationObserver = mutationObserver;
            this.cleanupInterval = null;
        }

        start() {
            this.cleanupInterval = setInterval(() => this.stateManager.cleanup(), CONSTANTS.CLEANUP_INTERVAL);
        }

        destroy() {
            if (this.cleanupInterval) clearInterval(this.cleanupInterval);
            this.stateManager.cleanup();
            this.hdrProcessor.cleanup();
            this.mutationObserver.stop();
        }
    }

    /**
     * Main application class.
     */
    class AutoHDRApp {
        constructor() {
            this.settings = { ...DEFAULT_SETTINGS };
            this.performanceMonitor = new PerformanceMonitor();
            this.stateManager = new ElementStateManager();
            this.hdrProcessor = new HDRProcessor(this.settings, this.stateManager, this.performanceMonitor);
            this.mutationObserver = new OptimizedMutationObserver(this.hdrProcessor, this.stateManager, this.performanceMonitor);
            this.memoryManager = new MemoryManager(this.stateManager, this.hdrProcessor, this.mutationObserver);
            this.ui = null;
            this.timers = new Map();
            this.debouncedSave = this.debounce(this.saveSettings.bind(this), 1000);
        }

        debounce(func, delay) {
            let timeoutId;
            return (...args) => {
                clearTimeout(timeoutId);
                timeoutId = setTimeout(() => func.apply(this, args), delay);
            };
        }

        async init() {
            console.log('HDR: Initializing optimized version...');
            await this.loadSettings();
            this.hdrProcessor.settings = this.settings; // Ensure processor has latest settings
            this.setupEventListeners();
            this.startProcessing();
            this.memoryManager.start();
            console.log('HDR: Initialization complete');
        }

        setupEventListeners() {
            document.addEventListener('visibilitychange', () => {
                if (document.hidden) this.pauseProcessing();
                else this.resumeProcessing();
            });
            window.addEventListener('beforeunload', () => this.cleanup());
            if (this.settings.adaptiveThrottling) this.startPerformanceMonitoring();
        }

        startPerformanceMonitoring() {
            const monitorInterval = setInterval(() => {
                const metrics = this.performanceMonitor.getMetrics();
                if (metrics.averageProcessingTime > 15) {
                    console.warn('HDR: High processing time, enabling performance mode.');
                    this.settings.performanceMode = true;
                    this.ui?.updatePerformanceMode(true);
                }
            }, 5000);
            this.timers.set('performanceMonitor', monitorInterval);
        }

        startProcessing() {
            setTimeout(() => this.hdrProcessor.applyHDR(), 500);
            this.mutationObserver.start();
            if (document.readyState !== 'complete') {
                window.addEventListener('load', () => setTimeout(() => this.hdrProcessor.applyHDR(), 1000));
            }
        }

        pauseProcessing() {
            this.mutationObserver.stop();
        }

        resumeProcessing() {
            this.mutationObserver.start();
            this.hdrProcessor.applyHDR();
        }

        async loadSettings() {
            try {
                const saved = await GM_getValue(CONSTANTS.STORAGE_KEY, '{}');
                this.settings = { ...DEFAULT_SETTINGS, ...JSON.parse(saved) };
            } catch (e) {
                console.error('HDR: Failed to load settings, using defaults.', e);
                this.settings = { ...DEFAULT_SETTINGS };
            }
        }

        saveSettings() {
            try {
                GM_setValue(CONSTANTS.STORAGE_KEY, JSON.stringify(this.settings));
            } catch (e) {
                console.error('HDR: Failed to save settings', e);
            }
        }

        updateSettings(newSettings) {
            Object.assign(this.settings, newSettings);
            this.hdrProcessor.settings = this.settings;
            this.saveSettings();
            this.hdrProcessor.applyHDR();
        }

        updateLiveSettings(newSettings) {
            Object.assign(this.settings, newSettings);
            this.hdrProcessor.settings = this.settings;
            this.hdrProcessor.updateCSSCustomProperties();
            this.debouncedSave();
        }

        cleanup() {
            this.timers.forEach(timerId => clearTimeout(timerId));
            this.timers.clear();
            this.memoryManager?.destroy();
        }

        getPerformanceMetrics() {
            return this.performanceMonitor.getMetrics();
        }
    }

    /**
     * Manages the user interface.
     */
    class OptimizedUI {
        constructor(app) {
            this.app = app;
            this.elements = {};
            this.isVisible = false;
            this.hideTimer = null;
            this.isDragging = false;
        }

        create() {
            this.addStyles();
            this.createToggleButton();
            this.createPanel();
            this.setupEventListeners();
        }

        addStyles() {
            GM_addStyle(`
                #hdr-toggle-opt { position: fixed; background: #007bff; color: white; border: none; padding: 8px 12px; border-radius: 4px; cursor: pointer; z-index: 10001; font-size: 12px; font-weight: bold; transition: all 0.2s ease; opacity: 0.8; }
                #hdr-toggle-opt:hover { opacity: 1; background: #0056b3; }
                #hdr-toggle-opt.hidden { opacity: 0; pointer-events: none; }
                #hdr-panel-opt { position: fixed; width: 250px; background: rgba(0, 0, 0, 0.9); color: white; padding: 15px; border-radius: 6px; z-index: 10000; font-family: Arial, sans-serif; font-size: 13px; opacity: 0; transform: scale(0.95); transition: opacity 0.2s ease, transform 0.2s ease; pointer-events: none; }
                #hdr-panel-opt.show { opacity: 1; transform: scale(1); pointer-events: auto; }
                .hdr-drag-handle { cursor: move; user-select: none; padding: 5px; margin: -15px -15px 10px -15px; background: rgba(255, 255, 255, 0.1); border-top-left-radius: 6px; border-top-right-radius: 6px; text-align: center; }
                .hdr-control { margin: 10px 0; }
                .hdr-control label { display: block; margin-bottom: 4px; font-size: 12px; }
                .hdr-control input[type="range"] { width: 100%; height: 4px; }
                .hdr-control input[type="checkbox"] { margin-right: 8px; }
                .hdr-value { color: #00ff00; font-weight: bold; }
                .hdr-perf-info { font-size: 11px; color: #ccc; margin-top: 10px; padding-top: 10px; border-top: 1px solid #444; }
            `);
        }

        createToggleButton() {
            const button = document.createElement('button');
            button.id = 'hdr-toggle-opt';
            button.textContent = 'HDR';
            button.title = 'HDR Settings';
            const { buttonTop, buttonLeft, buttonRight } = this.app.settings;
            button.style.top = `${buttonTop}px`;
            button.style.left = buttonLeft !== null ? `${buttonLeft}px` : 'auto';
            button.style.right = buttonRight !== null ? `${buttonRight}px` : 'auto';
            document.body.appendChild(button);
            this.elements.toggle = button;
        }

        createPanel() {
            const panel = document.createElement('div');
            panel.id = 'hdr-panel-opt';
            const s = this.app.settings;
            panel.style.top = `${s.panelTop}px`;
            panel.style.left = s.panelLeft !== null ? `${s.panelLeft}px` : 'auto';
            panel.style.right = s.panelRight !== null ? `${s.panelRight}px` : 'auto';

            panel.innerHTML = `
                <div class="hdr-drag-handle" style="font-weight: bold; color: #007bff;">HDR Pro</div>
                <div class="hdr-control"><label><input type="checkbox" id="enabled-opt" ${s.enabled ? 'checked' : ''}> Enable HDR</label></div>
                <div class="hdr-control"><label>Brightness: <span class="hdr-value" id="brightness-val-opt">${s.brightness}</span></label><input type="range" id="brightness-opt" min="0.5" max="2" step="0.01" value="${s.brightness}"></div>
                <div class="hdr-control"><label>Contrast: <span class="hdr-value" id="contrast-val-opt">${s.contrast}</span></label><input type="range" id="contrast-opt" min="0.5" max="2" step="0.01" value="${s.contrast}"></div>
                <div class="hdr-control"><label>Saturation: <span class="hdr-value" id="saturation-val-opt">${s.saturation}</span></label><input type="range" id="saturation-opt" min="0.5" max="2" step="0.01" value="${s.saturation}"></div>
                <div class="hdr-control"><label><input type="checkbox" id="livePreview-opt" ${s.livePreview ? 'checked' : ''}> Live Preview</label></div>
                <div class="hdr-control"><label><input type="checkbox" id="performanceMode-opt" ${s.performanceMode ? 'checked' : ''}> Performance Mode</label></div>
                <div class="hdr-perf-info" id="perf-info-opt">Performance: Loading...</div>`;

            document.body.appendChild(panel);
            this.elements.panel = panel;
        }

        setupEventListeners() {
            this.elements.toggle.addEventListener('click', () => this.togglePanel());
            this.elements.panel.addEventListener('input', this.handlePanelEvent.bind(this));
            this.elements.panel.addEventListener('change', this.handlePanelEvent.bind(this));
            this.setupDrag();
            this.setupAutoHide();
            this.startPerformanceInfoUpdate();
        }

        /**
         * Handles events from the control panel for sliders and checkboxes.
         * @param {Event} e - The input or change event.
         */
        handlePanelEvent(e) {
            const target = e.target;
            const eventType = e.type;

            if (target.type === 'range') {
                const setting = target.id.replace('-opt', '');
                const value = parseFloat(target.value);

                // Update the numeric display next to the slider
                const valueSpan = this.elements.panel.querySelector(`#${setting}-val-opt`);
                if (valueSpan) valueSpan.textContent = value.toFixed(2);

                // Apply settings based on event type and live preview setting
                if (eventType === 'input' && this.app.settings.livePreview) {
                    this.app.updateLiveSettings({ [setting]: value });
                } else if (eventType === 'change' && !this.app.settings.livePreview) {
                    this.app.updateSettings({ [setting]: value });
                }
            } else if (target.type === 'checkbox' && eventType === 'change') {
                const settingKey = target.id.replace('-opt', '');
                this.app.updateSettings({ [settingKey]: target.checked });
            }
        }

        setupDrag() {
            const handle = this.elements.panel.querySelector('.hdr-drag-handle');
            handle.addEventListener('mousedown', (e) => {
                if (e.target !== handle) return;
                e.preventDefault();
                this.isDragging = true;

                const panelRect = this.elements.panel.getBoundingClientRect();
                const offsetX = e.clientX - panelRect.left;
                const offsetY = e.clientY - panelRect.top;
                this.elements.panel.style.right = 'auto';

                const mouseMoveHandler = (moveEvent) => {
                    let newLeft = moveEvent.clientX - offsetX;
                    let newTop = moveEvent.clientY - offsetY;
                    newLeft = Math.max(0, Math.min(newLeft, window.innerWidth - panelRect.width));
                    newTop = Math.max(0, Math.min(newTop, window.innerHeight - panelRect.height));
                    this.elements.panel.style.left = `${newLeft}px`;
                    this.elements.panel.style.top = `${newTop}px`;
                };

                const mouseUpHandler = () => {
                    this.isDragging = false;
                    document.removeEventListener('mousemove', mouseMoveHandler);
                    document.removeEventListener('mouseup', mouseUpHandler);
                    const finalRect = this.elements.panel.getBoundingClientRect();
                    this.app.updateSettings({ panelTop: finalRect.top, panelLeft: finalRect.left, panelRight: null });
                };

                document.addEventListener('mousemove', mouseMoveHandler);
                document.addEventListener('mouseup', mouseUpHandler);
            });
        }

        setupAutoHide() {
            const hoverArea = document.createElement('div');
            hoverArea.style.cssText = `position: fixed; top: 0; right: 0; width: 80px; height: 50px; z-index: 9999;`;
            hoverArea.addEventListener('mouseenter', () => this.showToggle());
            document.body.appendChild(hoverArea);
            this.startAutoHideTimer();
        }

        startAutoHideTimer() {
            clearTimeout(this.hideTimer);
            this.hideTimer = setTimeout(() => {
                if (!this.isVisible && !this.isDragging) {
                    this.elements.toggle.classList.add('hidden');
                }
            }, this.app.settings.autoHideDelay);
        }

        showToggle() {
            this.elements.toggle.classList.remove('hidden');
            this.startAutoHideTimer();
        }

        togglePanel() {
            this.isVisible = !this.isVisible;
            this.elements.panel.classList.toggle('show', this.isVisible);
            if (this.isVisible) clearTimeout(this.hideTimer);
            else this.startAutoHideTimer();
        }

        startPerformanceInfoUpdate() {
            const perfInfo = this.elements.panel.querySelector('#perf-info-opt');
            if (!perfInfo) return;
            const intervalId = setInterval(() => {
                const metrics = this.app.getPerformanceMetrics();
                perfInfo.innerHTML = `Avg: ${metrics.averageProcessingTime.toFixed(1)}ms | Mem: ${(metrics.memoryUsage / 1024 / 1024).toFixed(1)}MB`;
            }, 2000);
            this.app.timers.set('perfInfo', intervalId);
        }

        updatePerformanceMode(isEnabled) {
            const checkbox = this.elements.panel.querySelector('#performanceMode-opt');
            if (checkbox) checkbox.checked = isEnabled;
        }
    }

    /**
     * Initializes the application.
     */
    function initialize() {
        if (!document.body) {
            setTimeout(initialize, 100);
            return;
        }

        const app = new AutoHDRApp();
        app.init().then(() => {
            const ui = new OptimizedUI(app);
            ui.create();
            app.ui = ui;
        }).catch(console.error);
    }

    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initialize);
    } else {
        initialize();
    }

    console.log('HDR: Optimized script loaded successfully');
})();
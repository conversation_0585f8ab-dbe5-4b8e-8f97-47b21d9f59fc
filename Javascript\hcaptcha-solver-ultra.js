// ==UserScript==
// @name        Auto hCaptcha Solver (Ultra)
// @namespace   Hcaptcha Solver
// @version     1.0
// @description Automatically solves hCaptcha challenges using advanced recognition.
// <AUTHOR> Md ubeadulla (Refactored and Enhanced by <PERSON>)
// @match       https://*.hcaptcha.com/*hcaptcha-challenge*
// @match       https://*.hcaptcha.com/*checkbox*
// @grant       GM.xmlHttpRequest
// @run-at      document-start
// @connect     www.imageidentify.com
// @connect     cdnjs.cloudflare.com
// @connect     cdn.jsdelivr.net
// @connect     unpkg.com
// @connect     *.hcaptcha.com/*
// @require     https://unpkg.com/jimp@0.22.10/browser/lib/jimp.min.js
// @require     https://cdnjs.cloudflare.com/ajax/libs/tesseract.js/5.0.5/tesseract.min.js
// @require     https://cdn.jsdelivr.net/npm/@tensorflow/tfjs@3.12.0/dist/tf.min.js
// @require     https://cdn.jsdelivr.net/npm/@tensorflow-models/coco-ssd@2.2.2/dist/coco-ssd.min.js
// @require     https://cdn.jsdelivr.net/npm/@tensorflow-models/mobilenet@2.1.0/dist/mobilenet.min.js
// ==/UserScript==

(async () => {
  "use strict";

  // --- Configuration ---
  const ENABLE_TENSORFLOW = true;
  const MAX_SKIPS = 10;
  const DEBUG = true; // Toggle verbose logging
  const LOG_PREFIX = "[hCaptcha Solver]";
  const WOLFRAM_API_URL = "https://www.imageidentify.com/objects/user-26a7681f-4b48-4f71-8f9f-93030898d70d/prd/urlapi/";


  // --- DOM Selectors ---
  const SELECTORS = {
    CHECKBOX: "#checkbox",
    SUBMIT_BUTTON: ".button-submit",
    TASK_IMAGE_BORDER: ".task-image .border",
    IMAGE: ".task-image .image",
    TASK_IMAGE: ".task-image",
    PROMPT_TEXT: ".prompt-text",
    NO_SELECTION: ".no-selection",
    CHALLENGE_INPUT_FIELD: ".challenge-input .input-field",
    CHALLENGE_INPUT: ".challenge-input",
    CHALLENGE_IMAGE: ".challenge-example .image .image",
    IMAGE_FOR_OCR: ".challenge-image .zoom-image",
  };

  // --- Constants ---
  const ARIA_CHECKED = "aria-checked";
  const ARIA_HIDDEN = "aria-hidden";
  const LANGUAGE_FOR_OCR = "eng";
  const SENTENCE_START_REGEX = /Please click each image containing a[n]? /i;

  // --- Object Categories & Synonyms ---
  const TRANSPORT_TYPES = ["airplane", "bicycle", "boat", "bus", "car", "motorbus", "motorcycle", "seaplane", "speedboat", "surfboard", "train", "trimaran", "truck"];
  const LIVING_ROOM_TYPES = ["bed", "book", "chair", "clock", "couch", "dining_table", "potted_plant", "tv"];
  const ANIMAL_TYPES = ["zebra"];

  const SYNONYM_MAP = {
    bus: { synonyms: ["bus", "motorbus"] },
    motorbus: { synonyms: ["bus", "motorbus"] },
    car: { synonyms: ["car", "coupe", "jeep", "limo", "sport utility vehicle", "station wagon", "hatchback", "bumper car", "modelT", "electric battery", "cruiser"] },
    airplane: { synonyms: ["airplane", "plane", "aircraft", "aeroplane", "hangar", "Airdock", "JumboJet", "jetliner", "stealth fighter", "field artillery"] },
    train: { synonyms: ["train", "rail", "cable car", "locomotive", "subway station"] },
    boat: { useMobileNet: true, synonyms: ["boat", "barge", "houseboat", "boathouse", "speedboat", "submarine", "bobsled", "catamaran", "schooner", "ocean liner", "lifeboat", "fireboat", "yawl", "pontoon", "small boat", "SnowBlower", "Sea-coast", "paddlewheel", "paddle wheel", "PaddleSteamer", "Freighter", "Sternwheeler", "kayak", "canoe", "deck", "DockingFacility", "surfboard", "ship", "cruise", "watercraft", "sail", "canvas", "raft"] },
    surfboard: { useMobileNet: true, synonyms: ["boat", "barge", "houseboat", "boathouse", "speedboat", "submarine", "bobsled", "catamaran", "schooner", "ocean liner", "lifeboat", "fireboat", "yawl", "pontoon", "small boat", "SnowBlower", "Sea-coast", "paddlewheel", "paddle wheel", "PaddleSteamer", "Freighter", "Sternwheeler", "kayak", "canoe", "deck", "DockingFacility", "surfboard", "ship", "cruise", "watercraft", "sail", "canvas", "raft"] },
    bicycle: { synonyms: ["bicycle", "tricycle", "mountain bike", "AcceleratorPedal", "macaw", "knot"] },
    motorcycle: { useMobileNet: true, synonyms: ["moped", "motor scooter", "scooter", "motorcycle", "windshield", "dashboard"] },
    truck: { synonyms: ["truck", "cargocontainer", "bazooka"] },
    trimaran: { useMobileNet: true, synonyms: ["stretcher", "printer", "nail", "mousetrap", "trimaran", "space shuttle", "ski", "rotisserie", "geyser", "plate rack"] },
    speedboat: { useMobileNet: true, synonyms: ["stretcher", "printer", "nail", "mousetrap", "trimaran", "space shuttle", "ski", "rotisserie", "geyser", "plate rack"] },
    seaplane: { useMobileNet: true, synonyms: ["stretcher", "printer", "nail", "mousetrap", "trimaran", "space shuttle", "ski", "rotisserie", "geyser", "plate rack"] },
  };


  // --- State ---
  let state = {
    selectedImageCount: 0,
    skipCount: 0,
    useMobileNet: false,
    tensorFlowModel: null,
    tensorFlowMobileNetModel: null,
    tesseractWorker: null,
    identifiedObjects: [],
    exampleImages: [],
    currentExampleUrls: [],
    prevPromptText: null,
    prevWord: null,
  };

  // --- Utility Functions ---
  const log = (...args) => DEBUG && console.log(LOG_PREFIX, ...args);
  const q = (selector) => document.querySelector(selector);
  const qAll = (selector) => document.querySelectorAll(selector);
  const isHidden = (el) => el.offsetParent === null;
  const delay = (ms) => new Promise((resolve) => setTimeout(resolve, ms));
  const getUrlFromString = (urlString) => urlString.match(/url\("([^"]+)"/)?.[1] ?? null;
  const includesOneOf = (str, arr) => arr.some((item) => str.toLowerCase().includes(item.toLowerCase()));
  const equalsOneOf = (str, arr) => arr.some((item) => str.toLowerCase() === item.toLowerCase());
  const arraysEqual = (a, b) => a.length === b.length && a.every((val, index) => val === b[index]);

  const triggerEvent = (el, type) => {
    const event = new Event(type, { bubbles: true, cancelable: false });
    el.dispatchEvent(event);
  };

  // --- Model Loading ---
  const loadModel = async (modelLoader, modelName) => {
    log(`Loading ${modelName} model...`);
    const model = await modelLoader();
    log(`${modelName} model loaded.`);
    return model;
  };

  const getTensorFlowModel = async () => {
    if (!state.tensorFlowModel) {
      state.tensorFlowModel = await loadModel(cocoSsd.load, "COCO-SSD");
    }
    return state.tensorFlowModel;
  };

  const getMobileNetModel = async () => {
    if (!state.tensorFlowMobileNetModel) {
      state.tensorFlowMobileNetModel = await loadModel(mobilenet.load, "MobileNet");
    }
    return state.tensorFlowMobileNetModel;
  };

  const getTesseractWorker = async () => {
    if (!state.tesseractWorker) {
      log("Initializing Tesseract worker...");
      state.tesseractWorker = await Tesseract.createWorker(LANGUAGE_FOR_OCR);
      log("Tesseract worker initialized.");
    }
    return state.tesseractWorker;
  };

  // --- Image Prediction ---
  const predictWithWolfram = (imageUrl, word, index) => {
    log(`Predicting image ${index + 1} with Wolfram...`);
    return new Promise((resolve, reject) => {
      GM.xmlHttpRequest({
        method: "POST",
        url: WOLFRAM_API_URL,
        headers: { "Content-Type": "application/x-www-form-urlencoded" },
        data: `image=${encodeURIComponent(imageUrl)}`,
        timeout: 8000,
        onload: (response) => {
          handleWolframResponse(response, imageUrl, word, index);
          resolve();
        },
        onerror: (e) => {
          log(`Wolfram API error on image ${index + 1}:`, e);
          reject(e);
        },
        ontimeout: () => {
          log(`Wolfram API timeout on image ${index + 1}`);
          reject(new Error("Timeout"));
        },
      });
    });
  };

  const handleWolframResponse = (response, imageUrl, word, index) => {
    try {
      const imageElements = qAll(SELECTORS.IMAGE);
      const borderElements = qAll(SELECTORS.TASK_IMAGE_BORDER);
      if (response?.responseText && imageElements[index]?.style.background.includes(imageUrl) && borderElements[index]?.style.opacity === "0") {
        const { identify } = JSON.parse(response.responseText);
        if (identify && (includesOneOf(identify.title, word) || includesOneOf(identify.entity, word) || (identify.alternatives && Object.values(identify.alternatives).some(alt => includesOneOf(alt, word))))) {
          log(`Image ${index + 1} matches target label via Wolfram. Clicking...`);
          qAll(SELECTORS.TASK_IMAGE)[index].click();
        } else {
          log(`Image ${index + 1} does not match target label via Wolfram.`);
        }
        state.selectedImageCount++;
      }
    } catch (err) {
      log(`Error parsing Wolfram response for image ${index + 1}:`, err);
    }
  };

  const predictWithTensorFlow = async (imageUrl, word, index, model, modelName) => {
    try {
      log(`Predicting image ${index + 1} with ${modelName}...`);
      const img = new Image();
      img.crossOrigin = "Anonymous";
      img.src = imageUrl;
      await new Promise((resolve, reject) => {
        img.onload = async () => {
          try {
            const predictions = modelName === "COCO-SSD" ? await model.detect(img) : await model.classify(img);
            if (predictions.some(p => includesOneOf(p.class || p.className, word))) {
              log(`Image ${index + 1} matches target label via ${modelName}. Clicking...`);
              qAll(SELECTORS.TASK_IMAGE)[index].click();
            } else {
              log(`Image ${index + 1} does not match target label via ${modelName}.`);
            }
            state.selectedImageCount++;
            resolve();
          } catch (err) {
            reject(err);
          } finally {
            img.removeAttribute("src");
          }
        };
        img.onerror = reject;
      });
    } catch (err) {
      log(`${modelName} prediction error on image ${index + 1}:`, err);
    }
  };


  // --- OCR Functions ---
  const preProcessImageForOCR = async (imageUrl) => {
    try {
      log("Preprocessing image for OCR...");
      const image = await Jimp.read(imageUrl);
      const worker = await getTesseractWorker();

      const transformations = [
        img => img.clone().color([{ apply: "darken", params: [20] }, { apply: "brighten", params: [20] }]).greyscale(),
        img => img.clone().color([{ apply: "darken", params: [20] }]).contrast(1).color([{ apply: "brighten", params: [20] }]).contrast(1).greyscale(),
        img => img.clone().contrast(1).color([{ apply: "brighten", params: [20] }]).contrast(1).greyscale(),
        img => img.clone().resize(256, Jimp.AUTO).quality(60).greyscale(),
      ];

      for (const transform of transformations) {
        const processedImage = transform(image);
        const src = await processedImage.getBase64Async(Jimp.AUTO);
        const { data: { text } } = await worker.recognize(src);
        if (text?.length > 0) {
          log("OCR preprocessing successful.");
          return text.replace(/[\n{}\[\]]/g, "");
        }
      }
      log("OCR preprocessing failed to yield text.");
    } catch (err) {
      log("Image preprocessing error:", err);
    }
    return null;
  };

  const performOCR = async () => {
    try {
      log("Starting OCR process...");
      const imageUrl = getUrlFromString(q(SELECTORS.IMAGE_FOR_OCR).style.background);
      if (!imageUrl) {
        log("No image URL found for OCR.");
        return selectImagesAfterDelay(1);
      }

      const processedText = await preProcessImageForOCR(imageUrl);
      if (processedText) {
        log("OCR Result:", processedText);
        const inputField = q(SELECTORS.CHALLENGE_INPUT_FIELD);
        inputField.value = processedText;
        triggerEvent(q(SELECTORS.CHALLENGE_INPUT), "input");
        q(SELECTORS.SUBMIT_BUTTON).click();
      } else {
        log("OCR failed to extract text.");
        selectImagesAfterDelay(1);
      }
    } catch (err) {
      log("OCR error:", err);
      selectImagesAfterDelay(1);
    }
  };

  // --- Challenge Logic ---
  const getSynonymsForWord = (word) => {
    log(`Getting synonyms for ${word}...`);
    state.useMobileNet = false;

    const entry = SYNONYM_MAP[word];
    if (entry) {
      state.useMobileNet = !!entry.useMobileNet;
      log(`Synonyms found: ${entry.synonyms.join(", ")}`);
      return entry.synonyms;
    }

    if (includesOneOf(word, LIVING_ROOM_TYPES)) return LIVING_ROOM_TYPES;
    if (includesOneOf(word, ANIMAL_TYPES)) return ANIMAL_TYPES;

    log(`Unknown word: ${word}, returning empty array.`);
    return [];
  };

  const identifyWordFromExamples = async () => {
    log("Identifying word from example images...");
    state.currentExampleUrls = Array.from(qAll(SELECTORS.CHALLENGE_IMAGE)).map(img => getUrlFromString(img.style.background));

    if (!state.currentExampleUrls.every(url => url) || arraysEqual(state.exampleImages, state.currentExampleUrls)) {
        log("Example images have not changed or are invalid.");
        return getWordFromIdentifiedObjects();
    }
    
    state.exampleImages = state.currentExampleUrls;
    if (state.exampleImages.length === 0) return null;

    const models = [{loader: getTensorFlowModel, name: "COCO-SSD"}, {loader: getMobileNetModel, name: "MobileNet"}];

    for(const modelInfo of models) {
        await identifyObjectsFromImages(state.exampleImages, modelInfo.loader, modelInfo.name);
        const word = getWordFromIdentifiedObjects();
        if(word) return word;
    }

    return null;
  };
  
  const identifyObjectsFromImages = async (imageUrlList, modelLoader, modelName) => {
      state.identifiedObjects = [];
      try {
          log(`Identifying objects from example images using ${modelName}...`);
          const model = await modelLoader();
          const imagePromises = imageUrlList.map(url => {
              return new Promise(async (resolve, reject) => {
                  const img = new Image();
                  img.crossOrigin = "Anonymous";
                  img.src = url;
                  img.onload = async () => {
                      try {
                          const predictions = modelName === "COCO-SSD" ? await model.detect(img) : await model.classify(img);
                          const objectClasses = predictions.map(p => p.class || p.className);
                          state.identifiedObjects.push(...objectClasses);
                          img.removeAttribute("src");
                          resolve();
                      } catch (e) {
                          reject(e);
                      }
                  };
                  img.onerror = reject;
              });
          });
          await Promise.all(imagePromises);
          log(`Object identification complete using ${modelName}.`);
      } catch (e) {
          log(`Error identifying objects from images using ${modelName}:`, e);
      }
  };

  const getWordFromIdentifiedObjects = () => {
    if (state.identifiedObjects.length === 0) return null;
    log("Getting most frequent object from identified objects...");
    const counts = state.identifiedObjects.reduce((acc, obj) => {
        acc[obj] = (acc[obj] || 0) + 1;
        return acc;
    }, {});
    
    const mostFrequentObject = Object.entries(counts).reduce((a, b) => (a[1] > b[1] ? a : b), [null, 0])[0];

    if (mostFrequentObject && (equalsOneOf(mostFrequentObject, TRANSPORT_TYPES) || equalsOneOf(mostFrequentObject, LIVING_ROOM_TYPES) || equalsOneOf(mostFrequentObject, ANIMAL_TYPES))) {
      log(`Most frequent object: ${mostFrequentObject}`);
      return mostFrequentObject;
    }

    log("No frequent, valid object found.");
    return null;
  };

  const identifyWord = async () => {
    log("Identifying target word...");
    const promptElement = q(SELECTORS.PROMPT_TEXT);
    const currentPromptText = promptElement ? promptElement.innerText : null;

    if (state.prevPromptText === currentPromptText && state.prevWord) {
        return state.prevWord;
    }
    state.prevPromptText = currentPromptText;

    let word = null;
    if (window.location.href.includes("&hl=en") && currentPromptText) {
        word = currentPromptText.replace(SENTENCE_START_REGEX, "");
    }
    
    if (!word || !equalsOneOf(word, [...TRANSPORT_TYPES, ...LIVING_ROOM_TYPES, ...ANIMAL_TYPES])) {
        word = await identifyWordFromExamples();
    }

    log(`Identified word: ${word}`);
    state.prevWord = word;
    return word;
  };

  const selectImages = async () => {
    if (qAll(SELECTORS.IMAGE).length !== 9 || q(SELECTORS.NO_SELECTION)?.getAttribute(ARIA_HIDDEN) !== "true") {
      log("Waiting for images to appear...");
      return waitForImagesToAppear();
    }

    log("Starting image selection process...");
    state.selectedImageCount = 0;

    try {
      await getTensorFlowModel();
      await getMobileNetModel();

      let word = await identifyWord();

      if (!word) {
        if (state.skipCount >= MAX_SKIPS) {
          log("Max skips reached. Cannot solve captcha.");
          return;
        }
        state.skipCount++;
        q(SELECTORS.SUBMIT_BUTTON)?.click();
        return selectImagesAfterDelay(5);
      }

      const synonyms = getSynonymsForWord(word);
      const imageList = Array.from(qAll(SELECTORS.IMAGE)).map(img => getUrlFromString(img.style.background));

      if (imageList.length !== 9 || !imageList.every(url => url)) {
        q(SELECTORS.SUBMIT_BUTTON)?.click();
        return selectImagesAfterDelay(5);
      }

      const predictionPromises = [];
      for (let i = 0; i < 9; i++) {
        if (ENABLE_TENSORFLOW) {
            const model = state.useMobileNet ? await getMobileNetModel() : await getTensorFlowModel();
            const modelName = state.useMobileNet ? "MobileNet" : "COCO-SSD";
            predictionPromises.push(predictWithTensorFlow(imageList[i], synonyms, i, model, modelName));
        } else {
            predictionPromises.push(predictWithWolfram(imageList[i], synonyms, i));
        }
      }
      await Promise.all(predictionPromises);

      waitUntilImageSelection();
    } catch (err) {
      log("Error selecting images:", err);
      selectImagesAfterDelay(5);
    }
  };

  const waitUntilImageSelection = () => {
    let intervalCount = 0;
    const interval = setInterval(() => {
      intervalCount++;
      if (state.selectedImageCount === 9) {
        clearInterval(interval);
        q(SELECTORS.SUBMIT_BUTTON)?.click();
        selectImagesAfterDelay(5);
      } else if (intervalCount > 8) {
        clearInterval(interval);
        selectImages(); // Retry
      }
    }, 3000);
  };

  const waitForImagesToAppear = () => {
    let checkCount = 0;
    const interval = setInterval(() => {
      checkCount++;
      if (qAll(SELECTORS.IMAGE).length === 9) {
        clearInterval(interval);
        selectImages();
      } else if (checkCount > 60) {
        clearInterval(interval);
      } else if (q(SELECTORS.CHALLENGE_INPUT_FIELD) && q(SELECTORS.NO_SELECTION)?.getAttribute(ARIA_HIDDEN) !== "true") {
        clearInterval(interval);
        performOCR();
      }
    }, 5000);
  };

  const selectImagesAfterDelay = (seconds) => setTimeout(selectImages, seconds * 1000);

  // --- Main Execution ---
  const main = async () => {
    log("Starting hCaptcha solver script...");
    await getTesseractWorker();

    if (window.location.href.includes("checkbox")) {
      const checkboxInterval = setInterval(() => {
        const checkbox = q(SELECTORS.CHECKBOX);
        if (checkbox && checkbox.getAttribute(ARIA_CHECKED) === "false" && !isHidden(checkbox)) {
          log("Clicking checkbox...");
          checkbox.click();
          clearInterval(checkboxInterval);
        }
      }, 5000);
    } else {
      await selectImages();
    }
  };

  main().catch(err => log("Main execution error:", err));

})();
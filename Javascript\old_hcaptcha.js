// ==UserScript==
// @name        Auto hCaptcha Solver
// @namespace   Hcaptcha Solver
// @version     0.6
// @description Automatically solves hCaptcha challenges in the browser.
// <AUTHOR> and Md ubeadulla (refactored by <PERSON><PERSON>)
// @match       https://*.hcaptcha.com/*hcaptcha-challenge*
// @match       https://*.hcaptcha.com/*checkbox*
// @grant       GM_xmlhttpRequest
// @run-at      document-start
// @connect     www.imageidentify.com
// @connect     cdnjs.cloudflare.com
// @connect     cdn.jsdelivr.net
// @connect     unpkg.com
// @connect     *.hcaptcha.com/*
// @require     https://unpkg.com/jimp@0.5.2/browser/lib/jimp.min.js
// @require     https://cdnjs.cloudflare.com/ajax/libs/tesseract.js/2.0.0-alpha.2/tesseract.min.js
// @require     https://cdn.jsdelivr.net/npm/@tensorflow/tfjs@3.12.0/dist/tf.min.js
// @require     https://cdn.jsdelivr.net/npm/@tensorflow-models/coco-ssd@2.2.2/dist/coco-ssd.min.js
// @require     https://cdn.jsdelivr.net/npm/@tensorflow-models/mobilenet@2.1.0/dist/mobilenet.min.js
// ==/UserScript==

(async () => {
  "use strict";

  const log = (...args) => console.log("[hCaptcha Solver]", ...args);

  // Constants
  const CHECK_BOX = "#checkbox";
  const SUBMIT_BUTTON = ".button-submit";
  const TASK_IMAGE_BORDER = ".task-image .border";
  const IMAGE = ".task-image .image";
  const TASK_IMAGE = ".task-image";
  const PROMPT_TEXT = ".prompt-text";
  const NO_SELECTION = ".no-selection";
  const CHALLENGE_INPUT_FIELD = ".challenge-input .input-field";
  const CHALLENGE_INPUT = ".challenge-input";
  const CHALLENGE_IMAGE = ".challenge-example .image .image";
  const IMAGE_FOR_OCR = ".challenge-image .zoom-image";
  const ARIA_CHECKED = "aria-checked";
  const ARIA_HIDDEN = "aria-hidden";
  const LANGUAGE_FOR_OCR = "eng";
  const MAX_SKIPS = 10;

  // Object categories
  const TRANSPORT_TYPES = [
    "airplane",
    "bicycle",
    "boat",
    "bus",
    "car",
    "motorbus",
    "motorcycle",
    "seaplane",
    "speedboat",
    "surfboard",
    "train",
    "trimaran",
    "truck",
  ];
  const LIVING_ROOM_TYPES = [
    "bed",
    "book",
    "chair",
    "clock",
    "couch",
    "dining_table",
    "potted_plant",
    "tv",
  ];
  const ANIMAL_TYPES = ["zebra"];

  const SENTENCE_START_REGEX = /Please click each image containing a[n]? /i;

  // Options
  const ENABLE_TENSORFLOW = true;

  let selectedImageCount = 0;
  let skipCount = 0;
  let useMobileNet = false;
  let tensorFlowModel = null;
  let tensorFlowMobileNetModel = null;
  let tesseractWorker = null;
  let identifiedObjects = [];
  let exampleImages = [];
  let currentExampleUrls = [];
  let prevPromptText = null;
  let prevWord = null;

  // Helper functions
  const q = (selector) => document.querySelector(selector);
  const qAll = (selector) => document.querySelectorAll(selector);
  const isHidden = (el) => el.offsetParent === null;
  const delay = (ms) => new Promise((resolve) => setTimeout(resolve, ms));

  const triggerEvent = (el, type) => {
    const e = document.createEvent("HTMLEvents");
    e.initEvent(type, false, true);
    el.dispatchEvent(e);
  };

  const triggerMouseEvent = (el, type) => {
    const e = document.createEvent("MouseEvent");
    e.initEvent(type, false, true);
    el.dispatchEvent(e);
  };

  const getUrlFromString = (urlString) => {
    const match = urlString.match(/url\("([^"]+)"/);
    return match ? match[1] : null;
  };

  const getImageList = () => {
    return Array.from(qAll(IMAGE)).map((img) =>
      getUrlFromString(img.style.background)
    );
  };

  const includesOneOf = (str, arr) =>
    arr.some((item) => str.toLowerCase().includes(item.toLowerCase()));
  const equalsOneOf = (str, arr) =>
    arr.some((item) => str.toLowerCase() === item.toLowerCase());

  // Model loading (with logging)
  const loadTensorFlowModel = async () => {
    if (!tensorFlowModel) {
      log("Loading COCO-SSD model...");
      tensorFlowModel = await cocoSsd.load();
      log("COCO-SSD model loaded.");
    }
    return tensorFlowModel;
  };

  const loadMobileNetModel = async () => {
    if (!tensorFlowMobileNetModel) {
      log("Loading MobileNet model...");
      tensorFlowMobileNetModel = await mobilenet.load();
      log("MobileNet model loaded.");
    }
    return tensorFlowMobileNetModel;
  };

  const initTesseract = async () => {
    if (!tesseractWorker) {
      log("Initializing Tesseract worker...");
      tesseractWorker = new Tesseract.TesseractWorker();
      log("Tesseract worker initialized.");
    }
    return tesseractWorker;
  };

  // Image prediction (with logging)
  const predictWithWolfram = (imageUrl, word, i) => {
    log(`Predicting image ${i + 1} with Wolfram...`);
    return new Promise((resolve, reject) => {
      GM_xmlhttpRequest({
        method: "POST",
        url: "https://www.imageidentify.com/objects/user-26a7681f-4b48-4f71-8f9f-93030898d70d/prd/urlapi/",
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
        },
        data: "image=" + encodeURIComponent(imageUrl),
        timeout: 8000,
        onload: (response) => {
          handleWolframResponse(response, imageUrl, word, i);
          resolve(); // Resolve the promise after handling the response
        },
        onerror: (e) => {
          log(`Wolfram API error on image ${i + 1}:`, e);
          reject(e);
        },
        ontimeout: () => {
          log(`Wolfram API timeout on image ${i + 1}`);
          reject(new Error("Timeout"));
        },
      });
    });
  };

  const handleWolframResponse = (response, imageUrl, word, i) => {
    try {
      if (
        response &&
        response.responseText &&
        qAll(IMAGE)[i] &&
        qAll(IMAGE)[i].style.background.includes(imageUrl) &&
        qAll(TASK_IMAGE_BORDER)[i].style.opacity === 0
      ) {
        const responseJson = JSON.parse(response.responseText);

        if (
          responseJson.identify &&
          ((responseJson.identify.title &&
            includesOneOf(responseJson.identify.title, word)) ||
            (responseJson.identify.entity &&
              includesOneOf(responseJson.identify.entity, word)) ||
            (responseJson.identify.alternatives &&
              Object.keys(responseJson.identify.alternatives).some(
                (key) =>
                  includesOneOf(
                    responseJson.identify.alternatives[key],
                    word
                  ) || includesOneOf(key, word)
              )))
        ) {
          log(`Image ${i + 1} matches target label. Clicking...`);
          qAll(TASK_IMAGE)[i].click();
        } else {
          log(`Image ${i + 1} does not match target label.`);
        }
        selectedImageCount++;
      }
    } catch (err) {
      log(`Error parsing Wolfram response for image ${i + 1}:`, err);
    }
  };

  const predictWithTensorFlow = async (imageUrl, word, i) => {
    try {
      log(`Predicting image ${i + 1} with TensorFlow...`);
      const img = new Image();
      img.crossOrigin = "Anonymous";
      img.src = imageUrl;
      img.onload = async () => {
        const predictions = await tensorFlowModel.detect(img);
        if (predictions.some((p) => includesOneOf(p.class, word))) {
          log(`Image ${i + 1} matches target label. Clicking...`);
          qAll(TASK_IMAGE)[i].click();
        } else {
          log(`Image ${i + 1} does not match target label.`);
        }
        img.removeAttribute("src");
        selectedImageCount++;
      };
    } catch (err) {
      log(`TensorFlow prediction error on image ${i + 1}:`, err);
    }
  };

  const predictWithMobileNet = async (imageUrl, word, i) => {
    try {
      log(`Predicting image ${i + 1} with MobileNet...`);
      const img = new Image();
      img.crossOrigin = "Anonymous";
      img.src = imageUrl;

      img.onload = async () => {
        const predictions = await tensorFlowMobileNetModel.classify(img);
        const probabilityThreshold = 0.077;
        const prob =
          probabilityForObject.get(predictions[0].className) ||
          probabilityThreshold;

        if (
          predictions.some(
            (p) => includesOneOf(p.className, word) && p.probability > prob
          )
        ) {
          log(`Image ${i + 1} matches target label. Clicking...`);
          qAll(TASK_IMAGE)[i].click();
        } else {
          log(`Image ${i + 1} does not match target label.`);
        }

        img.removeAttribute("src");
        selectedImageCount++;
      };
    } catch (err) {
      log(`MobileNet prediction error on image ${i + 1}:`, err);
    }
  };

  // OCR functions (with logging)
  const preProcessImage = async (imageUrl) => {
    try {
      log("Preprocessing image for OCR...");
      const image = await Jimp.read(imageUrl);

      const processedImages = [
        image
          .color([{ apply: "darken", params: [20] }])
          .color([{ apply: "brighten", params: [20] }])
          .greyscale(),
        image
          .color([{ apply: "darken", params: [20] }])
          .contrast(1)
          .color([{ apply: "brighten", params: [20] }])
          .contrast(1)
          .greyscale(),

        image
          .contrast(1)
          .color([{ apply: "brighten", params: [20] }])
          .contrast(1)
          .greyscale(),
        image.resize(256, Jimp.AUTO).quality(60).greyscale(),
      ];

      for (const processedImage of processedImages) {
        const src = await processedImage.getBase64Async(Jimp.AUTO);
        const img = document.createElement("img");
        img.setAttribute("src", src);

        const {
          data: { text },
        } = await tesseractWorker.recognize(img, LANGUAGE_FOR_OCR);
        img.removeAttribute("src");

        if (text && text.length > 0) {
          log("OCR preprocessing successful.");
          return postProcessImage(text);
        }
      }
      log("OCR preprocessing failed.");
    } catch (err) {
      log("Image preprocessing error:", err);
    }

    return null;
  };

  const postProcessImage = (text) => text.replace(/[\n{}\[\]]/g, "");

  const imageUsingOCR = async () => {
    try {
      log("Starting OCR process...");
      const urlString = q(IMAGE_FOR_OCR).style.background;
      const imageUrl = getUrlFromString(urlString);
      if (!imageUrl) {
        log("No image URL found for OCR.");
        return selectImagesAfterDelay(1);
      }

      const processedText = await preProcessImage(imageUrl);

      if (processedText) {
        inputChallenge(processedText, imageUrl);
      } else {
        log("OCR failed to extract text.");
        selectImagesAfterDelay(1);
      }
    } catch (err) {
      log("OCR error:", err);
      selectImagesAfterDelay(1);
    }
  };

  const inputChallenge = (text, imageUrl) => {
    try {
      if (q(IMAGE_FOR_OCR).style.background.includes(imageUrl)) {
        log("OCR Result:", text);
        const targetNode = q(CHALLENGE_INPUT_FIELD);
        targetNode.value = text;
        triggerEvent(q(CHALLENGE_INPUT), "input");
        q(SUBMIT_BUTTON).click();
      }
    } catch (err) {
      log("Error inputting challenge:", err);
    }
  };

  // Example image handling (with logging)
  const areExampleImageUrlsChanged = () => {
    log("Checking if example images have changed...");
    const prevUrls = exampleImages;
    currentExampleUrls = Array.from(qAll(CHALLENGE_IMAGE)).map((img) =>
      getUrlFromString(img.style.background)
    );

    if (
      !currentExampleUrls.every((url) => url) ||
      prevUrls.length !== currentExampleUrls.length ||
      !prevUrls.every((url, i) => url === currentExampleUrls[i])
    ) {
      log("Example images have changed.");
      return true;
    }

    log("Example images have not changed.");
    return false;
  };

  const identifyObjectsFromImages = async (imageUrlList, model) => {
    identifiedObjects = [];
    try {
      log(`Identifying objects from example images using ${model}...`);
      for (const imageUrl of imageUrlList) {
        const img = new Image();
        img.crossOrigin = "Anonymous";
        img.src = imageUrl;

        img.onload = async () => {
          const predictions =
            model === "coco-ssd"
              ? await tensorFlowModel.detect(img)
              : await tensorFlowMobileNetModel.classify(img);
          const objectClasses =
            model === "coco-ssd"
              ? predictions.map((p) => p.class)
              : predictions.map((p) => p.className);
          identifiedObjects.push(...objectClasses);
          img.removeAttribute("src");
        };
      }
      log(`Object identification complete using ${model}.`);
    } catch (e) {
      log(`Error identifying objects from images using ${model}:`, e);
    }
  };

  const getWordFromIdentifiedObjects = () => {
    log("Getting most frequent object from identified objects...");
    const counts = {};
    identifiedObjects.forEach((obj) => (counts[obj] = (counts[obj] || 0) + 1));
    const mostFrequentObject = Object.entries(counts).reduce(
      (a, b) => (a[1] > b[1] ? a : b),
      [null, 0]
    );

    if (
      mostFrequentObject[0] &&
      (equalsOneOf(mostFrequentObject[0], TRANSPORT_TYPES) ||
        equalsOneOf(mostFrequentObject[0], LIVING_ROOM_TYPES) ||
        equalsOneOf(mostFrequentObject[0], ANIMAL_TYPES))
    ) {
      log(`Most frequent object: ${mostFrequentObject[0]}`);
      return mostFrequentObject[0];
    }

    log("No frequent object found.");
    return null;
  };

  const identifyWordFromExamples = async () => {
    log("Identifying word from example images...");
    if (areExampleImageUrlsChanged()) {
      exampleImages = currentExampleUrls;
      if (exampleImages.length === 0) {
        return null;
      }

      await identifyObjectsFromImages(exampleImages, "coco-ssd");
      let word = getWordFromIdentifiedObjects();

      if (!word) {
        await identifyObjectsFromImages(exampleImages, "mobilenet");
        word = getWordFromIdentifiedObjects();
      }

      return word;
    } else {
      return getWordFromIdentifiedObjects();
    }
  };

  const isPromptTextChanged = () => {
    const currentPromptText = q(PROMPT_TEXT) ? q(PROMPT_TEXT).innerText : null;
    if (!prevPromptText && currentPromptText) {
      prevPromptText = currentPromptText;
      return true;
    }

    if (
      prevPromptText &&
      currentPromptText &&
      prevPromptText !== currentPromptText
    ) {
      prevPromptText = currentPromptText;
      return true;
    }

    return false;
  };

  const identifyWord = async () => {
    log("Identifying target word...");
    let word = null;

    try {
      if (window.location.href.includes("&hl=en")) {
        word = q(PROMPT_TEXT) ? q(PROMPT_TEXT).innerText : null;
        if (word) {
          word = word.replace(SENTENCE_START_REGEX, "");
          if (!equalsOneOf(word, TRANSPORT_TYPES)) {
            const img = await convertTextToImage(word);
            word = await convertImageToText(img);
            word = word.replace(SENTENCE_START_REGEX, "");
            if (!equalsOneOf(word, TRANSPORT_TYPES)) {
              word = await identifyWordFromExamples();
            }
          }
        }
      } else {
        word = await identifyWordFromExamples();
      }
      log(`Identified word: ${word}`);
    } catch (e) {
      log("Error identifying word:", e);
    }

    return word;
  };

  const getSynonyms = (word) => {
    log(`Getting synonyms for ${word}...`);
    useMobileNet = false;

    switch (word) {
      case "bus":
      case "motorbus":
        log("Synonyms: bus, motorbus");
        return ["bus", "motorbus"];
      case "car":
        log("Synonyms: car, coupe, jeep, limo, ..."); // Shortened for brevity
        return [
          "car",
          "coupe",
          "jeep",
          "limo",
          "sport utility vehicle",
          "station wagon",
          "hatchback",
          "bumper car",
          "modelT",
          "electric battery",
          "cruiser",
        ];
      case "airplane":
        log("Synonyms: airplane, plane, aircraft, ...");
        return [
          "airplane",
          "plane",
          "aircraft",
          "aeroplane",
          "hangar",
          "Airdock",
          "JumboJet",
          "jetliner",
          "stealth fighter",
          "field artillery",
        ];
      case "train":
        log("Synonyms: train, rail, cable car, ...");
        return ["train", "rail", "cable car", "locomotive", "subway station"];
      case "boat":
      case "surfboard":
        useMobileNet = true;
        log("Synonyms: boat, barge, houseboat, ...");
        return [
          "boat",
          "barge",
          "houseboat",
          "boathouse",
          "speedboat",
          "submarine",
          "bobsled",
          "catamaran",
          "schooner",
          "ocean liner",
          "lifeboat",
          "fireboat",
          "yawl",
          "pontoon",
          "small boat",
          "SnowBlower",
          "Sea-coast",
          "paddlewheel",
          "paddle wheel",
          "PaddleSteamer",
          "Freighter",
          "Sternwheeler",
          "kayak",
          "canoe",
          "deck",
          "DockingFacility",
          "surfboard",
          "ship",
          "cruise",
          "watercraft",
          "sail",
          "canvas",
          "raft",
        ];
      case "bicycle":
        log("Synonyms: bicycle, tricycle, mountain bike, ...");
        return [
          "bicycle",
          "tricycle",
          "mountain bike",
          "AcceleratorPedal",
          "macaw",
          "knot",
        ];
      case "motorcycle":
        useMobileNet = true;
        log("Synonyms: moped, motor scooter, scooter, ...");
        return [
          "moped",
          "motor scooter",
          "scooter",
          "motorcycle",
          "windshield",
          "dashboard",
        ];
      case "truck":
        log("Synonyms: truck, cargocontainer, bazooka");
        return ["truck", "cargocontainer", "bazooka"];
      case "trimaran":
      case "speedboat":
      case "seaplane":
        useMobileNet = true;
        log("Synonyms: stretcher, printer, nail, ...");
        return [
          "stretcher",
          "printer",
          "nail",
          "mousetrap",
          "trimaran",
          "space shuttle",
          "ski",
          "rotisserie",
          "geyser",
          "plate rack",
        ];
      default:
        if (includesOneOf(word, LIVING_ROOM_TYPES)) {
          log("Synonyms: bed, couch, chair, ...");
          return [
            "bed",
            "couch",
            "chair",
            "potted plant",
            "dining table",
            "clock",
            "tv",
            "book",
          ];
        } else if (includesOneOf(word, ANIMAL_TYPES)) {
          log("Synonyms: zebra");
          return ["zebra"];
        } else {
          log(`Unknown word: ${word}, returning empty array.`);
          return [];
        }
    }
  };

  const selectImages = async () => {
    if (
      qAll(IMAGE).length === 9 &&
      q(NO_SELECTION)?.getAttribute(ARIA_HIDDEN) !== "true"
    ) {
      log("Starting image selection process...");
      selectedImageCount = 0;

      try {
        await loadTensorFlowModel();
        await loadMobileNetModel();

        if (isPromptTextChanged() || !prevWord) {
          prevWord = await identifyWord();
        }

        let word = prevWord;

        if (!word) {
          if (skipCount >= MAX_SKIPS) {
            log("Max skips reached. Cannot solve captcha.");
            return;
          }
          skipCount++;
          q(SUBMIT_BUTTON)?.click();
          return selectImagesAfterDelay(5);
        }

        word = getSynonyms(word);

        const imageList = getImageList();
        if (imageList.length !== 9 || !imageList.every((url) => url)) {
          q(SUBMIT_BUTTON)?.click();
          return selectImagesAfterDelay(5);
        }

        const predictFunction = useMobileNet
          ? predictWithMobileNet
          : predictWithTensorFlow;

        for (let i = 0; i < 9; i++) {
          if (ENABLE_TENSORFLOW) {
            if (useMobileNet) {
              await predictWithMobileNet(imageList[i], word, i);
            } else {
              await predictWithTensorFlow(imageList[i], word, i);
            }
          } else {
            await predictWithWolfram(imageList[i], word, i);
          }
        }

        waitUntilImageSelection();
      } catch (err) {
        log("Error selecting images:", err);
        selectImagesAfterDelay(5);
      }
    } else {
      log("Waiting for images to appear...");
      waitForImagesToAppear();
    }
  };

  const waitUntilImageSelection = () => {
    let intervalCount = 0;
    const interval = setInterval(() => {
      intervalCount++;
      if (selectedImageCount === 9) {
        clearInterval(interval);
        q(SUBMIT_BUTTON)?.click();
        selectImagesAfterDelay(5);
      } else if (intervalCount > 8) {
        clearInterval(interval);
        selectImages();
      }
    }, 3000);
  };

  const waitForImagesToAppear = () => {
    let checkCount = 0;
    const interval = setInterval(() => {
      checkCount++;
      if (qAll(IMAGE).length === 9) {
        clearInterval(interval);
        selectImages();
      } else if (checkCount > 60) {
        clearInterval(interval);
      } else if (
        q(CHALLENGE_INPUT_FIELD) &&
        q(NO_SELECTION).getAttribute(ARIA_HIDDEN) !== "true"
      ) {
        clearInterval(interval);
        imageUsingOCR();
      } else {
        const targetNodeList = [
          "Yes",
          "3 or more items of furniture",
          "Equipped space or room",
          "Photo is clean, no watermarks, logos or text overlays",
          "An interior photo of room",
          "Unsure",
          "Photo is sharp",
        ];
        const targetNode = Array.from(qAll("div")).find((el) =>
          targetNodeList.includes(el.textContent)
        );
        if (targetNode) {
          clearInterval(interval);
          triggerMouseEvent(targetNode, "mousedown");
          triggerMouseEvent(targetNode, "mouseup");
          q(SUBMIT_BUTTON)?.click();
          selectImagesAfterDelay(1);
        }
      }
    }, 5000);
  };

  const selectImagesAfterDelay = (delay) =>
    setTimeout(selectImages, delay * 1000);

  // Main execution
  try {
    log("Starting hCaptcha solver script...");
    await initTesseract();
    if (window.location.href.includes("checkbox")) {
      const checkboxInterval = setInterval(() => {
        const checkbox = q(CHECK_BOX);
        if (
          checkbox &&
          checkbox.getAttribute(ARIA_CHECKED) === "false" &&
          !isHidden(checkbox)
        ) {
          log("Clicking checkbox...");
          checkbox.click();
          clearInterval(checkboxInterval);
        }
      }, 5000);
    } else {
      selectImages();
    }
  } catch (err) {
    log("Main error:", err);
  }
})();

// ==UserScript==
// @name         YouTube Audio Enhancer - Phantom V2
// @namespace    https://github.com/Moryata/userscripts
// @version      12.0
// @description  An invisible, click-to-cycle audio preset enhancer for YouTube and YouTube Music. Right-click the trigger to move it.
// <AUTHOR>
// @match        https://www.youtube.com/*
// @match        https://music.youtube.com/*
// @license      MIT
// @grant        GM_getValue
// @grant        GM_setValue
// @grant        GM_addStyle
// ==/UserScript==

(function() {
  'use strict';

  // --- PRESETS & AUDIO CONFIGURATION ---
  const PRESETS = [
  { name: "Off", settings: { gain: 1.0, bass: 1.0, treble: 1.0, presence: 1.0, compressor: { threshold: 0, knee: 0, ratio: 1, attack: 0, release: 0.25 } } },
  { name: "Clarity", settings: { gain: 1.04, bass: 0.99, treble: 1.12, presence: 1.08, compressor: { threshold: -36, knee: 25, ratio: 2.25, attack: 0.012, release: 0.22 } } },
  { name: "Bass Boost", settings: { gain: 1.08, bass: 1.35, treble: 0.98, presence: 1.02, compressor: { threshold: -42, knee: 36, ratio: 3.5, attack: 0.02, release: 0.42 } } },
  { name: "Loudness", settings: { gain: 1.12, bass: 1.15, treble: 1.08, presence: 1.05, compressor: { threshold: -46, knee: 36, ratio: 4.5, attack: 0.01, release: 0.5 } } },
  { name: "Vocal Boost", settings: { gain: 1.02, bass: 0.98, treble: 1.08, presence: 1.25, compressor: { threshold: -30, knee: 18, ratio: 1.8, attack: 0.003, release: 0.18 } } },
  ];
  const AUDIO_PARAMS = {
    BASS: { type: 'peaking', freq: 90, q: 1.2, gainMultiplier: 8 },
    TREBLE: { type: 'highshelf', freq: 9000, q: 0.8, gainMultiplier: 6 },
    PRESENCE: { type: 'peaking', freq: 3000, q: 1.5, gainMultiplier: 6 },
  };

  /**
   * --- CORE AUDIO ENHANCER CLASS (Phantom V2) ---
   */
  class AudioEnhancer {
    constructor() {
      this.ctx = null;
      this.nodes = {};
      this.cache = { video: null, trigger: null, nameDisplay: null };
      this.hideTimer = null;
      this.isInitialized = false; // Flag to check if the enhancer is active
      this.isMoving = false; // Flag for UI element dragging
      this.currentPresetIdx = 0; // Current active preset index
      this.source = null; // Audio source node
      this._mouse = { x: 0, y: 0 }; // Mouse coordinates for throttling
      this._rafScheduled = false; // Flag to prevent multiple rAF calls

      // Bind 'this' for all event handlers
      Object.getOwnPropertyNames(Object.getPrototypeOf(this))
        .filter(prop => prop.startsWith('handle'))
        .forEach(prop => this[prop] = this[prop].bind(this));
    }

    async init() {
      if (this.isInitialized) return; // Prevent re-initialization
      const video = document.querySelector('video.html5-main-video, video[src]');
      if (!video) return;

      this.cache.video = video;
      try {
        await this.setupAudioContext(video);
        this.injectUi();
        this.setupEventListeners();

        this.currentPresetIdx = GM_getValue('presetIdx', 0);
        this.applyPreset(this.currentPresetIdx, false); // Apply initial preset without showing name

        this.isInitialized = true;
        console.log(`🎵 Phantom V2 Initialized. Preset: ${PRESETS[this.currentPresetIdx].name}`);
      } catch (e) {
        console.error('🎵 Phantom V2 initialization failed:', e);
      }
    }

    cleanup() {
      if (!this.isInitialized) return; // Only cleanup if initialized
      document.removeEventListener('mousemove', this.handleGlobalMouseMove);
      document.removeEventListener('pointermove', this.handleGlobalMouseMove);
      // Disconnect media source if present to prevent memory leaks
      try {
        if (this.source && typeof this.source.disconnect === 'function') {
          this.source.disconnect();
        }
      } catch (e) {
        console.warn('Error disconnecting source:', e);
      }
      this.cache.trigger?.remove(); // Remove UI elements from DOM
      this.cache.nameDisplay?.remove();
      this.ctx?.close().catch(e => console.error('Error closing audio context:', e));
      cancelAnimationFrame(this._rafId);
      this.isInitialized = false;
      this.ctx = null;
      this.cache = {};
    }

    async setupAudioContext(video) {
      const AudioContext = window.AudioContext || window.webkitAudioContext;
      this.ctx = new AudioContext(); // Create new AudioContext
      if (this.ctx.state === 'suspended') await this.ctx.resume(); // Resume if suspended (e.g., due to user gesture policy)

      const source = this.ctx.createMediaElementSource(video); // Create source from video element
      this.source = source; // Store source node for disconnection
      this.nodes = {
        gain: this.ctx.createGain(),
        bass: this.createFilter(AUDIO_PARAMS.BASS),
        treble: this.createFilter(AUDIO_PARAMS.TREBLE),
        presence: this.createFilter(AUDIO_PARAMS.PRESENCE),
        compressor: this.ctx.createDynamicsCompressor(),
      };

      // Connect nodes in sequence: Source -> Gain -> Bass -> Treble -> Presence -> Compressor -> Destination
      source.connect(this.nodes.gain).connect(this.nodes.bass).connect(this.nodes.treble).connect(this.nodes.presence).connect(this.nodes.compressor).connect(this.ctx.destination);
    }

    createFilter({ type, freq, q }) {
      const filter = this.ctx.createBiquadFilter();
      filter.type = type; filter.frequency.value = freq; filter.Q.value = q; // Set filter parameters
      return filter;
    }

    applyPreset(index, showName = true) {
      if (!this.nodes.gain) return; // Ensure nodes are initialized
      const idx = ((index % PRESETS.length) + PRESETS.length) % PRESETS.length;
      const preset = PRESETS[idx];
      if (!preset) return;

      const { gain, bass, treble, presence, compressor } = preset.settings;
      this.nodes.gain.gain.value = gain;
      this.nodes.bass.gain.value = (bass - 1) * AUDIO_PARAMS.BASS.gainMultiplier;
      this.nodes.treble.gain.value = (treble - 1) * AUDIO_PARAMS.TREBLE.gainMultiplier;
      this.nodes.presence.gain.value = (presence - 1) * AUDIO_PARAMS.PRESENCE.gainMultiplier;
      for (const prop in compressor) {
        try { // Safely assign compressor properties
          const nodeProp = this.nodes.compressor[prop];
          if (nodeProp && typeof nodeProp.value !== 'undefined') {
            nodeProp.value = compressor[prop];
          } else if (typeof this.nodes.compressor[prop] !== 'undefined') {
            // fallback assignment
            this.nodes.compressor[prop] = compressor[prop];
          }
        } catch (e) { // Catch errors for individual property assignments
          // ignore individual property failures
        }
      }

      if (showName) this.showPresetName(preset.name); // Show preset name if requested
    }

    showPresetName(name) {
      if (!this.cache.nameDisplay) return;
      this.cache.nameDisplay.textContent = name;
      this.cache.nameDisplay.classList.add('visible');
      clearTimeout(this.nameTimer);
      this.nameTimer = setTimeout(() => {
        this.cache.nameDisplay.classList.remove('visible');
      }, 1500);
    }

    injectUi() {
      if (this.cache.trigger) return; // Prevent re-injecting UI

      const trigger = document.createElement('div');
      trigger.className = 'enhancer-trigger';
      this.cache.trigger = trigger;

      const nameDisplay = document.createElement('div');
      nameDisplay.className = 'enhancer-preset-name'; // Display for preset name
      this.cache.nameDisplay = nameDisplay;

      // Restore trigger position from GM_Value
      try {
        const raw = GM_getValue('triggerPos', '{ "x": "98vw", "y": "50vh" }');
        const pos = JSON.parse(raw);
        if (pos && pos.x && pos.y) {
          trigger.style.left = pos.x;
          trigger.style.top = pos.y;
        } else {
          trigger.style.left = '98vw';
          trigger.style.top = '50vh';
        }
      } catch (e) {
        trigger.style.left = '98vw';
        trigger.style.top = '50vh';
      }
      document.body.appendChild(trigger); // Append trigger to body
      document.body.appendChild(nameDisplay);
      GM_addStyle(this.getStyles()); // Inject CSS styles
    }

    setupEventListeners() {
      // Use pointermove for broader device support (mouse, touch, pen) and passive to avoid blocking
      document.addEventListener('pointermove', this.handleGlobalMouseMove, { passive: true });
      this.cache.trigger.addEventListener('click', this.handleClick);
      this.cache.trigger.addEventListener('contextmenu', this.handleRightClick);
    }

    // Throttled pointer/mouse handler using rAF to reduce work on high-frequency events
    handleGlobalMouseMove(e) {
      this._mouse.x = e.clientX;
      this._mouse.y = e.clientY;
      if (!this._rafScheduled) { // Schedule a single rAF for processing
        this._rafScheduled = true;
        this._rafId = requestAnimationFrame(() => this._processMouseFrame());
      }
    }

    _processMouseFrame() {
      this._rafScheduled = false; // Reset flag for next frame
      const eX = this._mouse.x; // Use cached mouse coordinates
      const eY = this._mouse.y;
      if (!this.cache.trigger) return;
      if (this.isMoving) {
        this.cache.trigger.style.left = `${eX}px`;
        this.cache.trigger.style.top = `${eY}px`;
        return;
      }
      const rect = this.cache.trigger.getBoundingClientRect();
      const dist = Math.hypot(eX - rect.left - rect.width / 2, eY - rect.top - rect.height / 2); // Distance from trigger center
      if (dist < 100) {
        this.showTrigger(); // Show trigger if mouse is close
        this.hideTrigger(); // Schedule hide after a delay
      }
    }

    handleClick(e) {
      if (this.isMoving) { // If dragging, stop dragging and save position
        e.preventDefault();
        this.isMoving = false;
        this.cache.trigger.classList.remove('moving');
        GM_setValue('triggerPos', JSON.stringify({ x: this.cache.trigger.style.left, y: this.cache.trigger.style.top }));
        return;
      }
      this.currentPresetIdx = (this.currentPresetIdx + 1) % PRESETS.length;
      this.applyPreset(this.currentPresetIdx); // Cycle to next preset
      GM_setValue('presetIdx', this.currentPresetIdx); // Save current preset index
    }

    handleRightClick(e) {
      e.preventDefault();
      this.isMoving = true; // Start dragging
      this.cache.trigger.classList.add('moving'); // Add visual feedback for dragging
    }

    showTrigger() {
      clearTimeout(this.hideTimer);
      this.cache.trigger.classList.add('visible');
    }

    hideTrigger() {
      if (this.isMoving) return; // Don't hide if currently dragging
      clearTimeout(this.hideTimer);
      this.hideTimer = setTimeout(() => {
        this.cache.trigger.classList.remove('visible');
      }, 2000);
    }

    getStyles() {
      return `
        .enhancer-trigger {
          position: fixed; transform: translate(-50%, -50%); /* Center the trigger */
          width: 50px; height: 50px; z-index: 9998; cursor: pointer;
          border-radius: 50%;
        }
        .enhancer-trigger::before {
          content: ''; position: absolute; top: 50%; left: 50%;
          transform: translate(-50%, -50%);
          width: 6px; height: 30px; background: white;
          border-radius: 3px; opacity: 0; /* Hidden by default */
          box-shadow: 0 0 12px white, 0 0 20px white;
          transition: opacity 0.4s, transform 0.4s;
        }
        .enhancer-trigger.visible::before {
          opacity: 0.6;
        }
        .enhancer-trigger:hover::before {
          opacity: 0.9;
        }
        .enhancer-trigger.moving::before { /* Animation for dragging state */
            opacity: 1; transform: translate(-50%, -50%) scale(1.2);
            animation: pulse 1.5s infinite;
        }
        @keyframes pulse {
            0% { box-shadow: 0 0 12px #fff, 0 0 20px #fff; }
            50% { box-shadow: 0 0 20px #0ff, 0 0 35px #0ff; }
            100% { box-shadow: 0 0 12px #fff, 0 0 20px #fff; }
        }
        .enhancer-preset-name {
          position: fixed; top: 50%; left: 50%;
          transform: translate(-50%, -50%) scale(0.9); /* Slightly smaller when hidden */
          padding: 12px 24px; background: rgba(20, 20, 20, 0.9);
          backdrop-filter: blur(12px); color: white; font-family: "YouTube Sans", "Roboto", sans-serif;
          font-size: 22px; font-weight: 700; letter-spacing: 0.5px;
          border-radius: 12px; z-index: 9999; pointer-events: none; /* Non-interactive */
          opacity: 0; transition: opacity 0.5s, transform 0.5s;
        }
        .enhancer-preset-name.visible {
          opacity: 1; transform: translate(-50%, -50%) scale(1);
        }
      `;
    }
  }

  // --- MAIN EXECUTION ---
  const audioEnhancer = new AudioEnhancer();

  // MutationObserver to detect video element changes (e.g., page navigation, video load)
  const observer = new MutationObserver(() => {
    clearTimeout(observer.debounce); // Debounce to prevent excessive calls
    observer.debounce = setTimeout(() => {
      const videoElement = document.querySelector('video.html5-main-video, video[src]');
      if (videoElement && !audioEnhancer.isInitialized) {
        audioEnhancer.init(); // Initialize if video found and not already initialized
      } else if (!videoElement && audioEnhancer.isInitialized) {
        audioEnhancer.cleanup(); // Cleanup if video removed and enhancer is active
      }
    }, 350);
  });
  observer.observe(document.body, { childList: true, subtree: true });

  setTimeout(() => {
    if (!audioEnhancer.isInitialized) {
        const videoElement = document.querySelector('video.html5-main-video, video[src]');
        if (videoElement) audioEnhancer.init();
    }
  }, 500);

})();

// ==UserScript==
// @name        Auto hCaptcha Solver
// @namespace   Hcaptcha Solver Similiar
// @version     1.8
// @description Automatically solves hCaptcha challenges.
// <AUTHOR>
// @match       https://*.hcaptcha.com/*hcaptcha-challenge*
// @match       https://*.hcaptcha.com/*checkbox*
// @grant       GM_xmlhttpRequest
// @grant       GM_addStyle
// @run-at      document-idle
// @connect     cdnjs.cloudflare.com
// @connect     cdn.jsdelivr.net
// @connect     unpkg.com
// @connect     *.hcaptcha.com/*
// @require     https://unpkg.com/resemblejs@3.2.3/resemble.js
// @require     https://html2canvas.hertzen.com/dist/html2canvas.min.js
// ==/UserScript==

(async () => {
  "use strict";

  const log = (...args) => console.log("[hCaptcha Solver]", ...args);
  const SIMILARITY_THRESHOLD = 0.7;
  const OCR_CACHE = new Map(); // Cache untuk hasil OCR

  const CHECK_BOX = "#checkbox";
  const SUBMIT_BUTTON = ".button-submit";
  const IMAGE = ".task-image .image";
  const TASK_IMAGE = ".task-image";
  const IMAGE_FOR_OCR = ".challenge-image .zoom-image";
  const PROMPT_TEXT = ".prompt-text";
  const ARIA_CHECKED = "aria-checked";

  const q = (selector) => document.querySelector(selector);
  const qAll = (selector) => document.querySelectorAll(selector);
  const isHidden = (el) => el.offsetParent === null;
  const getUrlFromString = (urlString) => {
    const match = urlString.match(/url\("([^"]+)"/);
    return match ? match[1] : null;
  };

  let tesseractWorker = null;
  let tesseractAvailable = true;

  const loadTesseract = () =>
    new Promise((resolve, reject) => {
      const script = document.createElement("script");
      script.src = "https://unpkg.com/tesseract.js@4.1.1/dist/tesseract.min.js";
      script.onload = resolve;
      script.onerror = reject;
      document.head.appendChild(script);
    });

  const initTesseract = async () => {
    if (tesseractWorker) return tesseractWorker;

    log("Initializing Tesseract...");
    try {
      await loadTesseract();
      tesseractWorker = await Tesseract.createWorker({
        tessedit_pageseg_mode: Tesseract.PSM_SINGLE_BLOCK, // Optimasi Tesseract
      });
      await tesseractWorker.loadLanguage("eng");
      await tesseractWorker.initialize("eng");
      await tesseractWorker.loadLanguage("ind");
      await tesseractWorker.initialize("ind");
      log("Tesseract initialized.");
      return tesseractWorker;
    } catch (e) {
      log("Tesseract initialization failed:", e);
      tesseractAvailable = false;
      return null;
    }
  };

  const recognizeText = async (image) => {
    if (!tesseractAvailable) return null;

    const cachedText = OCR_CACHE.get(image.src); // Periksa cache
    if (cachedText) {
      log("Using cached OCR result:", cachedText);
      return cachedText;
    }

    try {
      log("Recognizing text...");
      const {
        data: { text: indonesianText },
      } = await tesseractWorker.recognize(image.src, { lang: "ind" });
      if (indonesianText.trim() !== "") {
        OCR_CACHE.set(image.src, indonesianText.trim()); // Simpan di cache
        return indonesianText.trim();
      }

      const {
        data: { text: englishText },
      } = await tesseractWorker.recognize(image.src, { lang: "eng" });
      OCR_CACHE.set(image.src, englishText.trim()); // Simpan di cache
      return englishText.trim();
    } catch (e) {
      log("Error recognizing text:", e);
      return null;
    }
  };

  const getImageData = async (imageUrl) =>
    new Promise((resolve, reject) => {
      const img = new Image();
      img.crossOrigin = "Anonymous";
      img.onload = () => resolve(img);
      img.onerror = reject;
      img.src = imageUrl;
    });

  const compareImages = async (img1, img2) =>
    new Promise((resolve) => {
      resemble(img1.src)
        .compareTo(img2.src)
        .ignoreAntialiasing() // Optimasi perbandingan gambar
        .ignoreColors() // Optimasi perbandingan gambar
        .onComplete((data) =>
          resolve(parseFloat(data.misMatchPercentage) / 100)
        );
    });

  const extractKeywords = (text) => {
    const match = text.match(/berisi\s+(.+)/);
    return match ? match[1].trim() : null;
  };

  const recognizePromptText = async () => {
    if (!tesseractAvailable) return null;

    try {
      const promptElement = q(PROMPT_TEXT);
      if (!promptElement) return null;

      const promptImage = await html2canvas(promptElement);
      log("Recognizing prompt text...");
      const {
        data: { text },
      } = await tesseractWorker.recognize(promptImage);
      const recognizedText = text.trim().replace(/\s+/g, " ");
      log("Recognized prompt text:", recognizedText);
      return recognizedText;
    } catch (error) {
      log("Error recognizing prompt text:", error);
      return null;
    }
  };

  const selectImages = async () => {
    if (!(qAll(IMAGE).length === 9 && q(IMAGE_FOR_OCR))) {
      log("Waiting for images...");
      setTimeout(selectImages, 1000);
      return;
    }

    log("Starting image selection...");
    try {
      const exampleImageUrl = getUrlFromString(
        q(IMAGE_FOR_OCR).style.background
      );
      if (!exampleImageUrl) return;

      const exampleImageData = await getImageData(exampleImageUrl);
      const promptText = await recognizePromptText();
      if (!promptText) return;

      const exampleText = extractKeywords(promptText);
      if (!exampleText) return;

      const imageElements = qAll(TASK_IMAGE);
      const imageDataList = await Promise.all(
        Array.from(imageElements).map(async (imgElement, i) => {
          const currentImageUrl = getUrlFromString(
            imgElement.querySelector(IMAGE).style.background
          );
          return currentImageUrl
            ? {
                imageUrl: currentImageUrl,
                imageData: await getImageData(currentImageUrl),
                index: i,
              }
            : null;
        })
      );

      for (const imageData of imageDataList) {
        if (!imageData) continue;
        const { imageData: currentImageData, index: i } = imageData;

        if (exampleText && tesseractAvailable) {
          log(`Comparing image ${i + 1} based on text...`);
          const currentText = await recognizeText(currentImageData);
          if (currentText?.includes(exampleText)) {
            log(`Image ${i + 1} matches text. Clicking...`);
            imageElements[i].click();
            continue;
          }
          log(`Image ${i + 1} does not match text.`);
        }

        log(`Comparing image ${i + 1} based on similarity...`);
        const similarity = await compareImages(
          exampleImageData,
          currentImageData
        );
        log(`Image ${i + 1} similarity: ${similarity}`);

        if (1 - similarity >= SIMILARITY_THRESHOLD) {
          log(`Image ${i + 1} matches similarity threshold. Clicking...`);
          imageElements[i].click();
        } else {
          log(`Image ${i + 1} does not match similarity threshold.`);
        }
      }

      log("Image selection complete. Submitting...");
      q(SUBMIT_BUTTON)?.click();
    } catch (err) {
      log("Error selecting images:", err);
    }
  };

  try {
    log("Starting hCaptcha solver...");
    await initTesseract();

    if (window.location.href.includes("checkbox")) {
      const checkbox = q(CHECK_BOX);
      if (
        checkbox &&
        checkbox.getAttribute(ARIA_CHECKED) === "false" &&
        !isHidden(checkbox)
      ) {
        log("Clicking checkbox...");
        checkbox.click();
      }
    } else {
      selectImages();
    }
  } catch (err) {
    log("Main error:", err);
  }
})();

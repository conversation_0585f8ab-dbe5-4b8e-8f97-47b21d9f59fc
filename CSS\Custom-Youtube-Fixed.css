/* ==UserStyle==
@name           YouTube Ultra-Performance (Expanded)
@namespace      youtube.com
@version        1.3.0
@description    Absolute maximum performance CSS for a clean and fast YouTube, covering the entire UI. No visual frills.
<AUTHOR> Assistant
==/UserStyle== */

@-moz-document domain("youtube.com") {

/* ========================================
   CORE VARIABLES
   ======================================== */
:root {
    --yt-border-radius-light: 6px;
    --yt-border-radius-medium: 12px;
    --yt-border-radius-heavy: 16px;
    --yt-border-radius-round: 50%;
    --yt-spec-static-brand-red: #FF0000 !important;
    --yt-spec-wordmark-text: #FF0000 !important;
}

/* ========================================
   GENERAL & THUMBNAILS
   ======================================== */
.ytd-thumbnail, .yt-img-shadow, #thumbnail {
    border-radius: var(--yt-border-radius-medium) !important;
}

#time-status.ytd-thumbnail-overlay-time-status-renderer,
ytd-thumbnail-overlay-bottom-panel-renderer,
.ytp-ce-video-duration {
    border-radius: 0 var(--yt-border-radius-medium) 0 var(--yt-border-radius-light) !important;
    margin: 0 !important;
}

ytd-rich-item-renderer:hover {
    background-color: transparent !important;
    box-shadow: none !important;
}

/* ========================================
   AVATARS & CHANNELS
   ======================================== */
#avatar-btn, #avatar.ytd-topbar-menu-button-renderer, .ytd-video-owner-renderer .yt-img-shadow, .ytd-channel-renderer .yt-img-shadow, .ytd-comment-renderer .yt-img-shadow, .yt-live-chat-author-chip .yt-img-shadow {
    border-radius: var(--yt-border-radius-round) !important;
}

/* ========================================
   BUTTONS & CHIPS
   ======================================== */
.yt-spec-button-shape-next, yt-chip-cloud-chip-renderer, .yt-chip-shape, .ytp-button {
    border-radius: var(--yt-border-radius-light) !important;
}

.ytd-subscribe-button-renderer .yt-spec-button-shape-next--filled {
     border-radius: var(--yt-border-radius-heavy) !important;
}

/* ========================================
   SEARCH BAR
   ======================================== */
ytd-searchbox #container.ytd-searchbox {
    border-radius: var(--yt-border-radius-heavy) 0 0 var(--yt-border-radius-heavy);
}

ytd-searchbox #search-icon-legacy.ytd-searchbox {
    border-radius: 0 var(--yt-border-radius-heavy) var(--yt-border-radius-heavy) 0;
}

.sbsb_a, .sbdd_b {
    border-radius: 0 0 var(--yt-border-radius-medium) var(--yt-border-radius-medium);
}

/* ========================================
   PLAYER & VIDEO PAGE
   ======================================== */
#movie_player, .html5-video-player {
    border-radius: var(--yt-border-radius-heavy);
}

#description.ytd-watch-metadata {
    background: var(--yt-spec-surface-raised);
    border-radius: var(--yt-border-radius-heavy);
    padding: 16px;
    margin-top: 12px;
}

#info-text.ytd-video-primary-info-renderer {
    color: var(--yt-spec-text-secondary);
}

#chat.ytd-watch-flexy, #playlist.ytd-watch-flexy {
    border-radius: var(--yt-border-radius-heavy);
    background: var(--yt-spec-surface-raised);
    overflow: hidden;
}

#comments.ytd-watch-flexy {
    margin-top: 24px;
}

ytd-comment-thread-renderer {
    background: transparent;
    padding: 0;
    margin-bottom: 16px;
}

/* ========================================
   MENUS & POP-UPS (NO EXPENSIVE FX)
   ======================================== */
tp-yt-paper-dialog, tp-yt-paper-menu, tp-yt-paper-listbox, ytd-menu-popup-renderer, .ytp-settings-menu, ytd-multi-page-menu-renderer {
    border-radius: var(--yt-border-radius-heavy) !important;
    background: var(--yt-spec-surface-raised) !important; /* Solid background, no blur */
}

/* ========================================
   EXPANDED UI COVERAGE
   ======================================== */

/* --- Side Navigation Menu (Guide) --- */
ytd-guide-entry-renderer {
    border-radius: var(--yt-border-radius-medium);
}

/* --- End Screen Cards --- */
.ytp-ce-element {
    border-radius: var(--yt-border-radius-medium) !important;
}

/* --- Notifications Menu --- */
ytd-notification-renderer {
    border-radius: var(--yt-border-radius-medium);
    padding: 8px;
}

/* --- Add to Playlist Dialog --- */
ytd-add-to-playlist-renderer {
    border-radius: var(--yt-border-radius-heavy);
}
.tp-yt-paper-checkbox {
    border-radius: var(--yt-border-radius-light);
}

/* --- Channel Page --- */
.ytd-c4-tabbed-header-renderer #tabs-container {
    border-bottom: none;
}

/* --- General Containers & Pop-ups --- */
ytd-popup-container, .ytd-mealbar-promo-renderer, ytd-clarification-renderer {
    border-radius: var(--yt-border-radius-heavy) !important;
    background: var(--yt-spec-surface-raised) !important;
}

}

/* ==UserStyle==
@name:en            Bilibili Subtitle Edit
@name:ja            ビリビリ字幕スタイル編集
@name:zh-TW         Bilibili 字幕樣式編輯
@name               Bilibili Subtitle Edit
@namespace          github.com/user/repo
@version            1.0.0
<AUTHOR>
@description:en     Edit and enhance subtitle style for Bilibili videos
@description:ja     ビリビリ動画の字幕スタイルを編集・強化します
@description:zh-TW  編輯並增強 Bilibili 影片的字幕樣式
@description        Edit Bilibili Subtitle style
@license            MIT
==/UserStyle== */

@-moz-document domain("bilibili.tv") {
    /* Container: Center subtitle area at bottom */
    .subtitle_position.subtitle-position-bc .subtitle-wrap,
    div.subtitle-wrap,
    div.player-mobile-ass-subtitle,
    div.BILI-SUBTITLEX-ASS-box,
    .subtitle_position.subtitle-position-bc .subtitle-group,
    div.subtitle-group {
        display: flex !important;
        justify-content: center !important;
        align-items: flex-end !important;
    }

    /* Subtitle Text Styling */
    .subtitle_position.subtitle-position-bc .subtitle-item-text,
    span.subtitle-item-text,
    span.data-stroke-true {
        display: inline-block !important;
        text-align: center !important;
        color: #fff !important;
        font-family: "Netflix Sans", "Helvetica Neue", Helvetica, Arial, sans-serif !important;
        font-size: 38px !important;
        font-weight: 700 !important;
        line-height: 1.35 !important;
        letter-spacing: 0.5px !important;
        background: rgba(0, 0, 0, 0.75) !important; /* lebih gelap */
        border: none !important;
        border-radius: 0.6em !important;
        padding: 0.25em 0.9em !important;
        box-shadow: 0 4px 16px 0 rgba(0,0,0,0.45) !important;
        /* Shadow gelap menyebar, tanpa stroke */
        text-shadow:
            0 2px 8px rgba(0,0,0,0.95),
            0 0 8px #000 !important;
        pointer-events: auto;
        /* Tambahan: sedikit border putih tipis jika ingin lebih pop-out */
        /* border: 1.5px solid rgba(255,255,255,0.15) !important; */
    }

    /* Responsive: Mobile & Small Screens */
    @media (max-width: 600px) {
        .subtitle_position.subtitle-position-bc .subtitle-item-text,
        span.subtitle-item-text,
        span.data-stroke-true {
            font-size: 20px !important;
            padding: 0.13em 0.5em !important;
            border-radius: 0.4em !important;
        }
    }
}
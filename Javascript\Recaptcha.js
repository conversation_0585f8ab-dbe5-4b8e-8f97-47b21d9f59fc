// ==UserScript==
// @name         Recaptcha Audio Solver
// @namespace    RecaptchaAudioSolver
// @version      2.9.6
// @description  Otomatis menyelesaikan tantangan Recaptcha menggunakan metode audio.
// <AUTHOR> & Moryata
// @icon         https://www.google.com/s2/favicons?sz=64&domain=https://www.google.com/
// @match        *://*/recaptcha/*
// @connect      engageub.pythonanywhere.com
// @connect      engageub1.pythonanywhere.com
// @grant        GM_xmlhttpRequest
// @run-at       document-idle
// ==/UserScript==

(function () {
  'use strict';
  const MAX_ATTEMPTS_PER_SERVER = 3;
  const INTERVAL_DELAY_MS = 550;
  const SELECTORS = {
    CHECKBOX: '.recaptcha-checkbox-border',
    AUDIO_BUTTON: '#recaptcha-audio-button',
    IMAGE_BUTTON: '#recaptcha-image-button',
    IMA<PERSON>_SELECT: '#rc-imageselect',
    AUDIO_SOURCE: '#audio-source',
    RESPONSE_FIELD: '.rc-audiochallenge-response-field',
    AUDIO_RESPONSE: '#audio-response',
    AUDIO_ERROR_MESSAGE: ".rc-audiochallenge-error-message",
    RELOAD_BUTTON: "#recaptcha-reload-button",
    DOSCAPTCHA: ".rc-doscaptcha-body",
    VERIFY_BUTTON: "#recaptcha-verify-button",
  };
  const SERVERS = [
    'https://engageub.pythonanywhere.com',
    'https://engageub1.pythonanywhere.com',
  ];
  let solved = false,
    waiting = false,
    serverIdx = 0,
    serverTry = 0,
    audioUrl = "";
  const log = (msg) => console.log(`[Recaptcha Audio Solver] ${msg}`);
  const $ = (sel) => document.querySelector(sel);
  const isHidden = (el) => !el || el.offsetParent === null;

  function getTextFromAudio(url) {
    if (waiting || solved) return;
    waiting = true;
    serverTry++;
    const serverUrl = SERVERS[serverIdx];
    const processedUrl = url.replace("recaptcha.net", "google.com").replace("www.google.com", "google.com");
    log(`Kirim audio ke ${serverUrl} (Percobaan server ${serverTry} dari ${MAX_ATTEMPTS_PER_SERVER})`);

    GM_xmlhttpRequest({
      method: 'POST',
      url: serverUrl,
      headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
      data: 'input=' + encodeURIComponent(processedUrl) + '&lang=en',
      timeout: 60000,
      onload: (res) => {
        waiting = false;
        if (solved) return;

        if (!res || res.status !== 200) {
            log(`Server ${serverUrl} respon error: ${res ? res.status : 'No response'}`);
            return retryAudio();
        }
        const txt = res.responseText;
        if (!txt || txt.trim() === '' || txt === '0' || /[<>]/g.test(txt) || txt.length < 2 || txt.length > 50) {
             log(`Server ${serverUrl} respon tidak valid: "${txt}"`);
             return retryAudio();
        }
        const audioSourceElement = $(SELECTORS.AUDIO_SOURCE);
        const audioResponseField = $(SELECTORS.AUDIO_RESPONSE);
        const verifyButton = $(SELECTORS.VERIFY_BUTTON);
        if (!audioSourceElement || audioSourceElement.src !== audioUrl || !audioResponseField || !verifyButton) {
             log("Elemen DOM atau URL audio berubah sebelum input dapat diisi.");
             return retryAudio();
        }
        audioResponseField.value = txt;
        log(`Audio terisi: "${txt}"`);
        verifyButton.click();
        log("Verifikasi diklik.");
      },
      onerror: (err) => {
        waiting = false;
        log(`Server ${serverUrl} error: ${err.statusText || 'Unknown error'}`);
        retryAudio();
      },
      ontimeout: () => {
        waiting = false;
        log(`Server ${serverUrl} timeout.`);
        retryAudio();
      },
    });

    function retryAudio() {
      waiting = false;
      if (solved) return;
      const currentAudioSrc = $(SELECTORS.AUDIO_SOURCE)?.src;

      if (!currentAudioSrc || currentAudioSrc !== audioUrl) {
          log("Audio berubah saat mencoba lagi, batalkan retry untuk URL sebelumnya.");
          serverTry = 0;
          serverIdx = 0;
          audioUrl = "";
          return;
      }
      if (serverTry < MAX_ATTEMPTS_PER_SERVER) {
        log(`Coba lagi server yang sama (${serverUrl}). Sisa percobaan: ${MAX_ATTEMPTS_PER_SERVER - serverTry}`);
        getTextFromAudio(audioUrl);
      } else {
        log(`Semua percobaan (${MAX_ATTEMPTS_PER_SERVER}) pada server ${serverUrl} gagal untuk audio ini.`);
        serverTry = 0;
        serverIdx = (serverIdx + 1) % SERVERS.length;
        audioUrl = "";

        if (serverIdx === 0) {
          const imageButton = $(SELECTORS.IMAGE_BUTTON);
          if (imageButton && !isHidden(imageButton)) {
            log('Semua server gagal untuk audio ini, beralih ke gambar.');
            imageButton.click();
            solved = true;
          } else {
            log('Semua server gagal untuk audio ini, tetapi tombol gambar tidak ditemukan.');
          }
        } else {
          log('Coba server berikutnya.');
        }
      }
    }
  }

  setInterval(() => {
    if (solved) return;
    const doscaptchaBody = $(SELECTORS.DOSCAPTCHA);
    if (doscaptchaBody && !isHidden(doscaptchaBody) && doscaptchaBody.innerText) {
      log("Deteksi bot (DOSCAPTCHA), stop.");
      solved = true;
      return;
    }
    const checkbox = $(SELECTORS.CHECKBOX);
    if (checkbox && !isHidden(checkbox)) {
      checkbox.click();
    }
    const audioButton = $(SELECTORS.AUDIO_BUTTON);
    const imageSelect = $(SELECTORS.IMAGE_SELECT);
    const audioSourceElement = $(SELECTORS.AUDIO_SOURCE);
    const audioResponseField = $(SELECTORS.RESPONSE_FIELD);
    const reloadButton = $(SELECTORS.RELOAD_BUTTON);
    const errorMessage = $(SELECTORS.AUDIO_ERROR_MESSAGE);
    const audioInput = $(SELECTORS.AUDIO_RESPONSE);
    const verifyButton = $(SELECTORS.VERIFY_BUTTON);

    if (audioButton && !isHidden(audioButton) && (!audioResponseField || isHidden(audioResponseField))) {
      audioButton.click();
      log("Tombol audio diklik.");
      return;
    }

    if (audioResponseField && !isHidden(audioResponseField)) {
        const hasError = errorMessage && errorMessage.innerText && errorMessage.innerText.trim() !== "";
        const needReload = hasError || (!waiting && audioSourceElement && audioSourceElement.src && audioUrl === audioSourceElement.src);

        if (needReload && reloadButton && !reloadButton.disabled) {
             if ((!audioInput || audioInput.value.trim() === "") || hasError) {
                reloadButton.click();
                log(`Audio reloaded. Reason: ${hasError ? 'Error message detected' : 'Audio source stale/processing failed'}`);
                audioUrl = "";
                serverTry = 0;
                serverIdx = 0;
                waiting = false;
                return;
             }
        }
        if (!waiting && audioSourceElement && audioSourceElement.src && (!audioInput || audioInput.value.trim() === "") && audioUrl !== audioSourceElement.src) {
            audioUrl = audioSourceElement.src;
            log(`Audio baru terdeteksi: ${audioUrl}`);
            serverTry = 0;
            serverIdx = 0;
            getTextFromAudio(audioUrl);
        }
    }
    if (audioResponseField && !isHidden(audioResponseField) && audioInput && audioInput.value.trim() !== "" && verifyButton && !verifyButton.disabled && !isHidden(verifyButton)) {
        verifyButton.click();
    }
  }, INTERVAL_DELAY_MS);
})();
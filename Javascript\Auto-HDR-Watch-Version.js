// ==UserScript==
// @name         Auto HDR Ultra Optimized (Refactored)
// @namespace    http://taeparlaytampermonkey.net/
// @version      3.1
// @description  Ultra-optimized HDR effect with a clean, refactored codebase, enhanced performance, and improved UI.
// <AUTHOR> (Refactored by Gemini)
// @icon         data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' width='64' height='64'><rect width='100%' height='100%' rx='12' fill='%23007bff'/><text x='50%' y='54%' font-size='28' font-family='Arial' font-weight='bold' fill='white' text-anchor='middle' dominant-baseline='middle'>HDR</text></svg>
// @match        *://*.youtube.com/*
// @match        *://*.netflix.com/*
// @match        *://*.twitch.tv/*
// @match        *://*.vimeo.com/*
// @match        *://*.dailymotion.com/*
// @match        *://*.hulu.com/*
// @match        *://*.primevideo.com/*
// @match        *://*.disneyplus.com/*
// @match        *://*.max.com/*
// @match        *://*.crunchyroll.com/*
// @match        *://*.tiktok.com/*
// @match        *://*.instagram.com/*
// @match        *://*.facebook.com/*
// @match        *://*.bilibili.tv/*
// @grant        GM_getValue
// @grant        GM_setValue
// @grant        GM_addStyle
// @run-at       document-start
// ==/UserScript==

(function () {
    'use strict';

    // -----------------------------------------------------------------------------
    // SECTION: CONSTANTS AND CONFIGURATION
    // -----------------------------------------------------------------------------

    const SCRIPT_NAME = 'AutoHDRSettings';

    const DEFAULT_SETTINGS = {
        hdrEnabled: true,
        brightness: 1.05,
        contrast: 1.05,
        saturation: 1.15,
        vibrance: 0.30,
        highlightReduction: 0.35,
        highlightThreshold: 240,
        shadowBoost: 0.15,
        shadowThreshold: 50,
        colorTemperature: 0,
        sharpness: 0,
        antiNoise: 0.3,
        smoothing: 0.2,
        upscaleSmoothing: true,
        videoEnhancement: true,
        excludedSites: [],
        maxCanvasDimension: 2000,
        processSVGs: false,
        enableGUISettings: true,
        lazyProcessing: true,
        processOnlyVisible: true,
        autoDetectHDR: true,
        presets: {
            natural: { brightness: 1.02, contrast: 1.08, saturation: 1.02, vibrance: 0.15, antiNoise: 0.1, smoothing: 0.1, upscaleSmoothing: true, videoEnhancement: true },
            vivid: { brightness: 1.08, contrast: 1.25, saturation: 1.15, vibrance: 0.45, antiNoise: 0.2, smoothing: 0.15, upscaleSmoothing: true, videoEnhancement: true },
            cinema: { brightness: 0.95, contrast: 1.20, saturation: 1.10, vibrance: 0.25, antiNoise: 0.4, smoothing: 0.3, upscaleSmoothing: true, videoEnhancement: true },
            gaming: { brightness: 1.10, contrast: 1.30, saturation: 1.20, vibrance: 0.40, antiNoise: 0.3, smoothing: 0.2, sharpness: 20, upscaleSmoothing: true, videoEnhancement: true }
        }
    };

    const UI_IDS = {
        panel: 'autohdr-settings-panel',
        button: 'autohdr-settings-button',
        saveButton: 'autohdr-save-settings',
        notification: 'autohdr-notification',
        // Input IDs match settings keys
        ...Object.keys(DEFAULT_SETTINGS).reduce((acc, key) => ({ ...acc, [key]: key }), {})
    };

    const SATURATION_WEIGHTS = [0.299, 0.587, 0.114];

    // -----------------------------------------------------------------------------
    // SECTION: STATE MANAGEMENT
    // -----------------------------------------------------------------------------

    let settings = { ...DEFAULT_SETTINGS };
    const processedElements = new WeakSet();
    const crossOriginCache = new Map();
    let intersectionObserver = null;
    let mutationObserver = null;
    let cssFilterString = '';
    let siteExclusionCache = null;
    let lastHref = '';

    // -----------------------------------------------------------------------------
    // SECTION: SETTINGS MANAGEMENT
    // -----------------------------------------------------------------------------

    function loadSettings() {
        const saved = GM_getValue(SCRIPT_NAME, null);
        if (saved) {
            try {
                const parsed = JSON.parse(saved);
                settings = { ...DEFAULT_SETTINGS, ...parsed };
            } catch (e) {
                console.error("AutoHDR: Error parsing saved settings, using defaults.", e);
                settings = { ...DEFAULT_SETTINGS };
            }
        } else {
            settings = { ...DEFAULT_SETTINGS };
        }
        validateAndNormalizeSettings();
    }

    function saveSettings() {
        GM_setValue(SCRIPT_NAME, JSON.stringify(settings));
        window.dispatchEvent(new CustomEvent('autoHDRSettingsChanged'));
    }

    function validateAndNormalizeSettings() {
        const numericFields = {
            brightness: parseFloat, contrast: parseFloat, saturation: parseFloat, vibrance: parseFloat,
            highlightReduction: parseFloat, highlightThreshold: val => parseInt(val, 10),
            shadowBoost: parseFloat, shadowThreshold: val => parseInt(val, 10),
            colorTemperature: val => parseInt(val, 10), sharpness: val => parseInt(val, 10),
            antiNoise: parseFloat, smoothing: parseFloat, maxCanvasDimension: val => parseInt(val, 10)
        };

        for (const [field, parser] of Object.entries(numericFields)) {
            const parsed = parser(settings[field]);
            settings[field] = isNaN(parsed) ? DEFAULT_SETTINGS[field] : parsed;
        }

        const clamp = (val, min, max) => Math.max(min, Math.min(max, val));
        settings.shadowBoost = clamp(settings.shadowBoost, 0, 1);
        settings.shadowThreshold = clamp(settings.shadowThreshold, 0, 255);
        settings.colorTemperature = clamp(settings.colorTemperature, -100, 100);
        settings.sharpness = clamp(settings.sharpness, 0, 100);
        settings.antiNoise = clamp(settings.antiNoise, 0, 1);
        settings.smoothing = clamp(settings.smoothing, 0, 1);
        settings.vibrance = clamp(settings.vibrance, 0, 1);

        Object.keys(DEFAULT_SETTINGS).forEach(key => {
            if (typeof DEFAULT_SETTINGS[key] === 'boolean') {
                settings[key] = Boolean(settings[key]);
            }
        });

        if (!Array.isArray(settings.excludedSites)) {
            settings.excludedSites = DEFAULT_SETTINGS.excludedSites;
        }
        if (!settings.presets || typeof settings.presets !== 'object') {
            settings.presets = DEFAULT_SETTINGS.presets;
        }
    }

    // -----------------------------------------------------------------------------
    // SECTION: CORE HDR LOGIC
    // -----------------------------------------------------------------------------

    function applyHDREffectToImage(img) {
        if (img.dataset.hdrApplied || processedElements.has(img)) return;
        if (!img.complete || img.naturalWidth === 0) {
            if (img.complete) img.dataset.hdrApplied = 'invalid-dimensions';
            return;
        }
        if (settings.autoDetectHDR && img.src.toLowerCase().includes('hdr')) {
            img.dataset.hdrApplied = 'skipped-hdr-detected';
            processedElements.add(img);
            return;
        }
        if (!isElementVisible(img)) return;

        const src = img.src;
        if ((src.includes('.svg') || src.startsWith('data:image/svg+xml')) && !settings.processSVGs) {
            img.dataset.hdrApplied = 'skipped-svg';
            processedElements.add(img);
            return;
        }

        if (img.naturalWidth > settings.maxCanvasDimension || img.naturalHeight > settings.maxCanvasDimension || isCrossOrigin(img)) {
            applyCssFilter(img);
            img.dataset.hdrApplied = img.naturalWidth > settings.maxCanvasDimension ? 'css-only-large' : 'css-only-crossorigin';
            processedElements.add(img);
            return;
        }

        processImageWithCanvas(img);
    }

    function processImageWithCanvas(img) {
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d', { willReadFrequently: true });
        canvas.width = img.naturalWidth;
        canvas.height = img.naturalHeight;

        try {
            ctx.drawImage(img, 0, 0);
            const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
            
            applyPixelManipulation(imageData.data);

            if (settings.antiNoise > 0 || settings.smoothing > 0) {
                applyNoiseReductionAndSmoothing(imageData, canvas.width, canvas.height, settings.antiNoise, settings.smoothing);
            }

            ctx.putImageData(imageData, 0, 0);

            if (settings.sharpness > 0) {
                canvas.style.filter = `contrast(${1 + settings.sharpness / 200}) brightness(${1 + settings.sharpness / 400})`;
            }

            updateImageSource(img, canvas);

        } catch (e) {
            console.error("AutoHDR: Canvas processing failed.", e);
            applyCssFilter(img);
            img.dataset.hdrApplied = 'error-canvas-fallback';
            processedElements.add(img);
        }
    }
    
    function applyPixelManipulation(data) {
        const {
            brightness, contrast, saturation, vibrance, highlightReduction, highlightThreshold,
            shadowBoost, shadowThreshold, colorTemperature
        } = settings;

        const contrastOffset = 128;
        const tempFactor = colorTemperature / 100;
        const warmth = tempFactor > 0 ? tempFactor * 20 : 0;
        const coolness = tempFactor < 0 ? -tempFactor * 20 : 0;

        for (let i = 0; i < data.length; i += 4) {
            let r = data[i], g = data[i + 1], b = data[i + 2];

            // 1. Contrast & Brightness
            r = ((r - contrastOffset) * contrast + contrastOffset) * brightness;
            g = ((g - contrastOffset) * contrast + contrastOffset) * brightness;
            b = ((b - contrastOffset) * contrast + contrastOffset) * brightness;

            // 2. Shadow Boost
            if (shadowBoost > 0) {
                const luminance = SATURATION_WEIGHTS[0] * r + SATURATION_WEIGHTS[1] * g + SATURATION_WEIGHTS[2] * b;
                if (luminance < shadowThreshold) {
                    const shadowFactor = 1 + shadowBoost * (1 - luminance / shadowThreshold);
                    r *= shadowFactor; g *= shadowFactor; b *= shadowFactor;
                }
            }

            // 3. Color Temperature
            if (colorTemperature !== 0) {
                r += warmth - coolness;
                g += (warmth / 2) - (coolness / 4);
                b += -(warmth * 0.75) + coolness;
            }

            // 4. Saturation & Vibrance
            if (saturation !== 1.0 || vibrance > 0) {
                const gray = SATURATION_WEIGHTS[0] * r + SATURATION_WEIGHTS[1] * g + SATURATION_WEIGHTS[2] * b;
                let sat = saturation;
                if (vibrance > 0) {
                    const maxChannel = Math.max(r, g, b);
                    const minChannel = Math.min(r, g, b);
                    const currentSaturation = maxChannel > 0 ? (maxChannel - minChannel) / maxChannel : 0;
                    const isWarmTone = r > g && g > b;
                    const skinToneProtection = isWarmTone ? 0.3 : 1.0;
                    const vibranceStrength = vibrance * (1 - currentSaturation * 0.7) * skinToneProtection;
                    sat += vibranceStrength;
                }
                r = gray + (r - gray) * sat;
                g = gray + (g - gray) * sat;
                b = gray + (b - gray) * sat;
            }

            // 5. Highlight Reduction
            if (highlightReduction > 0) {
                const invReduction = 1 - highlightReduction;
                if (r > highlightThreshold) r = highlightThreshold + (r - highlightThreshold) * invReduction;
                if (g > highlightThreshold) g = highlightThreshold + (g - highlightThreshold) * invReduction;
                if (b > highlightThreshold) b = highlightThreshold + (b - highlightThreshold) * invReduction;
            }

            // 6. Clamp values
            data[i] = r < 0 ? 0 : r > 255 ? 255 : r;
            data[i + 1] = g < 0 ? 0 : g > 255 ? 255 : g;
            data[i + 2] = b < 0 ? 0 : b > 255 ? 255 : b;
        }
    }

    function applyNoiseReductionAndSmoothing(imageData, width, height, noiseStrength, smoothStrength) {
        // This is a complex operation. The original implementation is kept for its functionality.
        // For brevity in this refactoring example, the original code is maintained here.
        // A full refactor might explore WebGL or WebAssembly for higher performance.
        const data = imageData.data;
        const tempData = new Uint8ClampedArray(data);

        if (noiseStrength > 0) {
            for (let y = 1; y < height - 1; y++) {
                for (let x = 1; x < width - 1; x++) {
                    const idx = (y * width + x) * 4;
                    let rSum = 0, gSum = 0, bSum = 0, weightSum = 0;
                    for (let dy = -1; dy <= 1; dy++) {
                        for (let dx = -1; dx <= 1; dx++) {
                            const sampleIdx = ((y + dy) * width + (x + dx)) * 4;
                            const r = tempData[sampleIdx], g = tempData[sampleIdx + 1], b = tempData[sampleIdx + 2];
                            const spatialWeight = 1.0 / (1.0 + dx * dx + dy * dy);
                            const intensityDiff = Math.abs(r - tempData[idx]) + Math.abs(g - tempData[idx + 1]) + Math.abs(b - tempData[idx + 2]);
                            const intensityWeight = Math.exp(-intensityDiff / (50 * noiseStrength));
                            const weight = spatialWeight * intensityWeight;
                            rSum += r * weight; gSum += g * weight; bSum += b * weight;
                            weightSum += weight;
                        }
                    }
                    if (weightSum > 0) {
                        const factor = noiseStrength;
                        data[idx] = tempData[idx] * (1 - factor) + (rSum / weightSum) * factor;
                        data[idx + 1] = tempData[idx + 1] * (1 - factor) + (gSum / weightSum) * factor;
                        data[idx + 2] = tempData[idx + 2] * (1 - factor) + (bSum / weightSum) * factor;
                    }
                }
            }
        }

        if (smoothStrength > 0) {
            const tempData2 = new Uint8ClampedArray(data);
            const kernel = [1, 2, 1, 2, 4, 2, 1, 2, 1];
            const kernelSum = 16;
            for (let y = 1; y < height - 1; y++) {
                for (let x = 1; x < width - 1; x++) {
                    const idx = (y * width + x) * 4;
                    let rSum = 0, gSum = 0, bSum = 0;
                    let kernelIdx = 0;
                    for (let dy = -1; dy <= 1; dy++) {
                        for (let dx = -1; dx <= 1; dx++) {
                            const sampleIdx = ((y + dy) * width + (x + dx)) * 4;
                            const weight = kernel[kernelIdx++];
                            rSum += tempData2[sampleIdx] * weight;
                            gSum += tempData2[sampleIdx + 1] * weight;
                            bSum += tempData2[sampleIdx + 2] * weight;
                        }
                    }
                    const factor = smoothStrength;
                    data[idx] = tempData2[idx] * (1 - factor) + (rSum / kernelSum) * factor;
                    data[idx + 1] = tempData2[idx + 1] * (1 - factor) + (gSum / kernelSum) * factor;
                    data[idx + 2] = tempData2[idx + 2] * (1 - factor) + (bSum / kernelSum) * factor;
                }
            }
        }
    }

    function applyHDRToVideo(video) {
        if (!isElementVisible(video) || video.dataset.hdrApplied === 'video-processed') return;
        
        applyCssFilter(video);
        video.dataset.hdrApplied = 'video-processed';
        processedElements.add(video);

        if (settings.videoEnhancement) {
            enhanceVideoPlayback(video);
        }
    }

    function enhanceVideoPlayback(video) {
        if (video.dataset.qualityEnhanced) return;

        video.style.imageRendering = 'auto';
        video.classList.add('hdr-enhanced-video');

        const onPlay = () => video.style.willChange = 'filter, transform';
        const onPause = () => video.style.willChange = 'auto';
        const onLoadedMetadata = () => {
            if (video.videoWidth < 720 && settings.upscaleSmoothing) {
                video.style.transform = 'scale(1.001)';
            }
        };

        video.addEventListener('play', onPlay);
        video.addEventListener('pause', onPause);
        video.addEventListener('loadedmetadata', onLoadedMetadata);

        video.dataset.qualityEnhanced = 'true';
    }

    function updateImageSource(img, canvas) {
        if (!img.dataset.originalSrc && !img.src.startsWith('data:')) {
            img.dataset.originalSrc = img.src;
        }
        try {
            canvas.toBlob(blob => {
                if (!blob) { throw new Error("Blob creation failed"); }
                const blobUrl = URL.createObjectURL(blob);
                const prevBlob = img.dataset._hdrBlobUrl;
                img.src = blobUrl;
                img.dataset._hdrBlobUrl = blobUrl;
                img.dataset.hdrApplied = 'canvas-processed';
                processedElements.add(img);

                if (prevBlob) {
                    img.addEventListener('load', () => URL.revokeObjectURL(prevBlob), { once: true });
                }
            }, 'image/png');
        } catch (e) {
            img.src = canvas.toDataURL();
            img.dataset.hdrApplied = 'canvas-processed-fallback';
            processedElements.add(img);
        }
    }

    function revertElement(el) {
        if (!el.dataset.hdrApplied) return;

        el.style.filter = '';
        if (el.dataset.originalSrc) {
            el.src = el.dataset.originalSrc;
        }
        if (el.dataset._hdrBlobUrl) {
            try { URL.revokeObjectURL(el.dataset._hdrBlobUrl); } catch (e) {}
        }

        if (el.tagName === 'VIDEO') {
            el.classList.remove('hdr-enhanced-video');
            el.style.imageRendering = '';
            el.style.transform = '';
            el.style.willChange = '';
        }

        ['data-hdrApplied', 'data-originalSrc', 'data-_hdrBlobUrl', 'data-qualityEnhanced', 'data-hdrListener'].forEach(attr => el.removeAttribute(attr));
        processedElements.delete(el);
    }

    // -----------------------------------------------------------------------------
    // SECTION: HELPER FUNCTIONS
    // -----------------------------------------------------------------------------

    const debounce = (fn, delay) => {
        let timeoutId;
        return (...args) => {
            clearTimeout(timeoutId);
            timeoutId = setTimeout(() => fn(...args), delay);
        };
    };

    function isCrossOrigin(img) {
        const src = img.src;
        if (crossOriginCache.has(src)) return crossOriginCache.get(src);
        if (crossOriginCache.size > 1000) {
            crossOriginCache.delete(crossOriginCache.keys().next().value);
        }
        try {
            if (src.startsWith('data:')) return false;
            const isCrossOrigin = new URL(src, window.location.href).origin !== window.location.origin;
            crossOriginCache.set(src, isCrossOrigin);
            return isCrossOrigin;
        } catch (e) {
            crossOriginCache.set(src, true);
            return true;
        }
    }

    const isElementVisible = (element) => !settings.processOnlyVisible || 
        (rect => rect.top < window.innerHeight && rect.bottom > 0 && rect.left < window.innerWidth && rect.right > 0)
        (element.getBoundingClientRect());

    function getCssFilterString() {
        if (cssFilterString) return cssFilterString;
        
        let filter = `brightness(${settings.brightness}) contrast(${settings.contrast}) saturate(${settings.saturation + (settings.vibrance * 0.5)})`;
        
        if (settings.videoEnhancement) {
            if (settings.antiNoise > 0) filter += ` blur(${settings.antiNoise * 0.5}px)`;
            if (settings.smoothing > 0) filter += ` contrast(${1 + settings.smoothing * 0.1})`;
            if (settings.sharpness > 0) filter += ` contrast(${1 + settings.sharpness / 100}) brightness(${1 + settings.sharpness / 400})`;
        }
        
        cssFilterString = filter;
        return filter;
    }

    function applyCssFilter(element) {
        element.style.filter = getCssFilterString();
    }

    function isSiteExcluded() {
        const currentHref = window.location.href;
        if (currentHref === lastHref && siteExclusionCache !== null) {
            return siteExclusionCache;
        }
        lastHref = currentHref;
        if (!Array.isArray(settings.excludedSites) || settings.excludedSites.length === 0) {
            siteExclusionCache = false;
            return false;
        }
        siteExclusionCache = settings.excludedSites.some(site =>
            site && typeof site === 'string' && site.trim() !== '' && currentHref.includes(site.trim())
        );
        return siteExclusionCache;
    }

    // -----------------------------------------------------------------------------
    // SECTION: MEDIA PROCESSING & OBSERVERS
    // -----------------------------------------------------------------------------

    const debouncedProcessMedia = debounce(processAllMedia, 100);

    function processAllMedia() {
        if (!settings.hdrEnabled || isSiteExcluded()) {
            document.querySelectorAll('[data-hdrApplied]').forEach(revertElement);
            cleanupObservers();
            return;
        }

        if (settings.lazyProcessing) setupIntersectionObserver();

        // Process Images
        Array.from(document.images).forEach(img => {
            if (img.dataset.hdrApplied) return;
            if (settings.lazyProcessing && intersectionObserver) {
                intersectionObserver.observe(img);
                return;
            }
            if (img.complete) {
                applyHDREffectToImage(img);
            } else if (!img.dataset.hdrListener) {
                const onLoad = () => { applyHDREffectToImage(img); img.removeAttribute('data-hdrListener'); };
                const onError = () => { img.dataset.hdrApplied = 'error-load'; img.removeAttribute('data-hdrListener'); };
                img.addEventListener('load', onLoad, { once: true });
                img.addEventListener('error', onError, { once: true });
                img.dataset.hdrListener = 'true';
            }
        });

        // Process Videos
        document.querySelectorAll('video:not([data-hdrApplied])').forEach(applyHDRToVideo);
    }

    function setupIntersectionObserver() {
        if (!settings.lazyProcessing || intersectionObserver) return;
        intersectionObserver = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const el = entry.target;
                    if (el.tagName === 'IMG') applyHDREffectToImage(el);
                    else if (el.tagName === 'VIDEO') applyHDRToVideo(el);
                    intersectionObserver.unobserve(el);
                }
            });
        }, { rootMargin: '50px', threshold: 0.1 });
    }

    function startMutationObserver() {
        if (mutationObserver) mutationObserver.disconnect();
        if (!settings.hdrEnabled || isSiteExcluded()) {
            document.querySelectorAll('[data-hdrApplied]').forEach(revertElement);
            cleanupObservers();
            return;
        }

        mutationObserver = new MutationObserver(mutations => {
            let needsProcessing = false;
            for (const mutation of mutations) {
                if (mutation.type === 'childList' && mutation.addedNodes.length) {
                    for (const node of mutation.addedNodes) {
                        if (node.nodeType === 1 && (node.matches('img, video') || node.querySelector('img, video'))) {
                            needsProcessing = true;
                            break;
                        }
                    }
                } else if (mutation.type === 'attributes') {
                    const target = mutation.target;
                    if (target.dataset.hdrApplied && (mutation.attributeName === 'src' || mutation.attributeName === 'style')) {
                        revertElement(target);
                        needsProcessing = true;
                    }
                }
                if (needsProcessing) break;
            }
            if (needsProcessing) debouncedProcessMedia();
        });

        const observeTarget = document.body || document.documentElement;
        if (observeTarget) {
            mutationObserver.observe(observeTarget, { childList: true, subtree: true, attributes: true, attributeFilter: ['src', 'style'] });
        } else {
            document.addEventListener('DOMContentLoaded', () => {
                mutationObserver.observe(document.body, { childList: true, subtree: true, attributes: true, attributeFilter: ['src', 'style'] });
            }, { once: true });
        }
        debouncedProcessMedia();
    }

    function cleanupObservers() {
        if (intersectionObserver) {
            intersectionObserver.disconnect();
            intersectionObserver = null;
        }
        if (mutationObserver) {
            mutationObserver.disconnect();
            mutationObserver = null;
        }
    }

    // -----------------------------------------------------------------------------
    // SECTION: SETTINGS UI
    // -----------------------------------------------------------------------------

    function createSettingsGUI() {
        if (document.getElementById(UI_IDS.button) || !document.body) return;

        injectStyles();

        const panel = createElement('div', { id: UI_IDS.panel });
        const header = createElement('div', { className: 'autohdr-header' }, [createElement('div', { className: 'autohdr-title', textContent: 'Auto HDR Settings' })]);
        const content = createElement('div', { className: 'autohdr-content' });
        
        // Build UI sections
        content.appendChild(createCheckboxLabel('Enable HDR', { id: UI_IDS.hdrEnabled }));
        content.appendChild(createSection('Basic Adjustments', [
            createInput('Brightness', { id: UI_IDS.brightness, type: 'number', step: '0.01', min: '0' }),
            createInput('Contrast', { id: UI_IDS.contrast, type: 'number', step: '0.01', min: '0' }),
            createInput('Saturation', { id: UI_IDS.saturation, type: 'number', step: '0.01', min: '0' }),
            createInput('Vibrance', { id: UI_IDS.vibrance, type: 'number', step: '0.01', min: '0', max: '1' })
        ]));
        content.appendChild(createSection('Highlights & Shadows', [
            createInput('Highlight Reduction', { id: UI_IDS.highlightReduction, type: 'number', step: '0.01', min: '0', max: '1' }),
            createInput('Highlight Threshold', { id: UI_IDS.highlightThreshold, type: 'number', step: '1', min: '0', max: '255' }),
            createInput('Shadow Boost', { id: UI_IDS.shadowBoost, type: 'number', step: '0.01', min: '0', max: '1' }),
            createInput('Shadow Threshold', { id: UI_IDS.shadowThreshold, type: 'number', step: '1', min: '0', max: '255' })
        ]));
        content.appendChild(createSection('Color & Detail', [
            createInput('Color Temperature', { id: UI_IDS.colorTemperature, type: 'number', step: '1', min: '-100', max: '100' }),
            createInput('Sharpness', { id: UI_IDS.sharpness, type: 'number', step: '1', min: '0', max: '100' })
        ]));
        content.appendChild(createSection('Image Enhancement', [
            createInput('Anti-Noise', { id: UI_IDS.antiNoise, type: 'number', step: '0.01', min: '0', max: '1' }),
            createInput('Smoothing', { id: UI_IDS.smoothing, type: 'number', step: '0.01', min: '0', max: '1' }),
            createCheckboxLabel('Upscale Smoothing', { id: UI_IDS.upscaleSmoothing }),
            createCheckboxLabel('Video Enhancement', { id: UI_IDS.videoEnhancement })
        ], 'enhancement'));
        content.appendChild(createPresetsSection());
        content.appendChild(createSection('Performance & Options', [
            createInput('Max Canvas Dim (px)', { id: UI_IDS.maxCanvasDimension, type: 'number', step: '100', min: '200' }),
            createCheckboxLabel('Process SVGs (Canvas)', { id: UI_IDS.processSVGs }),
            createCheckboxLabel('Lazy Processing (Viewport)', { id: UI_IDS.lazyProcessing }),
            createCheckboxLabel('Process Only Visible', { id: UI_IDS.processOnlyVisible }),
            createCheckboxLabel('Auto-detect HDR Content', { id: UI_IDS.autoDetectHDR })
        ]));
        content.appendChild(createSection('Exclusions', [
            createInput('Excluded Sites (comma-separated)', { id: UI_IDS.excludedSites, title: "e.g., 'google.com, anothersite.net/path'" }, 'textarea')
        ]));

        const saveButton = createElement('button', { id: UI_IDS.saveButton, textContent: 'Save & Apply' });
        saveButton.addEventListener('click', handleSave);
        content.appendChild(saveButton);

        panel.append(header, content);
        document.body.appendChild(panel);

        const button = createElement('button', { id: UI_IDS.button, textContent: '⚡', title: 'HDR Settings' });
        document.body.appendChild(button);

        setupUIEventListeners(button, panel);
        populateGUISettings();
    }

    function setupUIEventListeners(button, panel) {
        let hoverTimeout;
        const showPanel = () => {
            clearTimeout(hoverTimeout);
            panel.style.display = 'flex';
            setTimeout(() => panel.classList.add('show'), 10);
            populateGUISettings();
        };
        const hidePanel = () => {
            panel.classList.remove('show');
            setTimeout(() => panel.style.display = 'none', 300);
        };

        button.addEventListener('mouseenter', showPanel);
        button.addEventListener('mouseleave', () => hoverTimeout = setTimeout(hidePanel, 300));
        panel.addEventListener('mouseenter', () => clearTimeout(hoverTimeout));
        panel.addEventListener('mouseleave', () => hoverTimeout = setTimeout(hidePanel, 300));
        document.addEventListener('click', (event) => {
            if (panel.classList.contains('show') && !panel.contains(event.target) && !button.contains(event.target)) {
                hidePanel();
            }
        });
    }

    function handleSave() {
        const updates = {};
        for (const key in DEFAULT_SETTINGS) {
            const el = document.getElementById(key);
            if (el) {
                if (el.type === 'checkbox') {
                    updates[key] = el.checked;
                } else if (key === 'excludedSites') {
                    updates[key] = el.value.split(',').map(s => s.trim()).filter(Boolean);
                } else {
                    const parser = typeof DEFAULT_SETTINGS[key] === 'number' && !Number.isInteger(DEFAULT_SETTINGS[key]) ? parseFloat : parseInt;
                    updates[key] = parser(el.value) ?? DEFAULT_SETTINGS[key];
                }
            }
        }
        Object.assign(settings, updates);
        validateAndNormalizeSettings();
        
        crossOriginCache.clear();
        siteExclusionCache = null;
        cssFilterString = '';

        saveSettings();
        showNotification('Settings saved successfully!');
        document.getElementById(UI_IDS.panel)?.classList.remove('show');
    }

    function populateGUISettings() {
        for (const [key, value] of Object.entries(settings)) {
            const el = document.getElementById(key);
            if (el) {
                if (el.type === 'checkbox') {
                    el.checked = value;
                } else if (Array.isArray(value)) {
                    el.value = value.join(', ');
                } else if (typeof value === 'number' && !Number.isInteger(value)) {
                    el.value = value.toFixed(2);
                }
                 else {
                    el.value = value;
                }
            }
        }
    }

    function createPresetsSection() {
        const container = createElement('div', { className: 'autohdr-presets-container' });
        const presetConfig = {
            natural: { icon: '🌿', desc: 'Subtle enhancement' },
            vivid: { icon: '🌈', desc: 'Bright & colorful' },
            cinema: { icon: '🎬', desc: 'Cinematic tones' },
            gaming: { icon: '🎮', desc: 'Sharp & responsive' }
        };

        Object.keys(settings.presets).forEach((name, index) => {
            const config = presetConfig[name] || { icon: '⚙️', desc: 'Custom' };
            const btn = createElement('button', { className: 'autohdr-preset-btn', style: `animation-delay: ${index * 0.1}s` }, [
                createElement('span', { className: 'preset-icon', textContent: config.icon }),
                createElement('span', { className: 'preset-name', textContent: name }),
                createElement('span', { className: 'preset-desc', textContent: config.desc })
            ]);
            btn.addEventListener('click', (e) => applyPreset(name, e));
            container.appendChild(btn);
        });
        
        const section = createSection('Quick Presets', []);
        section.querySelector('.autohdr-control-group').appendChild(container);
        return section;
    }

    function applyPreset(presetName, event) {
        const preset = settings.presets[presetName];
        if (!preset) return;

        Object.assign(settings, preset);
        populateGUISettings();
        handleSave();
        
        const btn = event.currentTarget;
        btn.classList.add('clicked');
        setTimeout(() => btn.classList.remove('clicked'), 600);
        showNotification(`${presetName.charAt(0).toUpperCase() + presetName.slice(1)} preset applied!`);
    }

    function showNotification(message, type = 'success') {
        let notification = document.querySelector('.autohdr-notification');
        if (!notification) {
            notification = createElement('div', { className: 'autohdr-notification' });
            document.body.appendChild(notification);
        }
        notification.className = `autohdr-notification ${type}`;
        notification.textContent = message;
        notification.classList.add('show');
        setTimeout(() => {
            notification.classList.remove('show');
        }, 3000);
    }

    // UI Element Creation Helpers
    function createElement(tag, props = {}, children = []) {
        const el = document.createElement(tag);
        Object.entries(props).forEach(([key, value]) => {
            if (key === 'textContent') el.textContent = value;
            else el.setAttribute(key, value);
        });
        children.forEach(child => el.appendChild(child));
        return el;
    }

    function createInput(labelText, inputProps, inputTag = 'input') {
        const input = createElement(inputTag, inputProps);
        const label = createElement('label', {}, [createElement('span', { textContent: labelText + ': ' }), input]);
        return label;
    }

    function createCheckboxLabel(labelText, inputProps) {
        const input = createElement('input', { type: 'checkbox', ...inputProps });
        const label = createElement('label', {}, [input, document.createTextNode(' ' + labelText)]);
        return label;
    }

    function createSection(title, children, dataSection = '') {
        const group = createElement('div', { className: 'autohdr-control-group' }, children);
        const sectionTitle = createElement('div', { className: 'autohdr-section-title', textContent: title, 'data-section': dataSection });
        return createElement('div', {}, [sectionTitle, group]);
    }

    // -----------------------------------------------------------------------------
    // SECTION: STYLES
    // -----------------------------------------------------------------------------

    function injectStyles() {
        GM_addStyle(`
            :root {
                --hdr-blue: #3b82f6;
                --hdr-purple: #8b5cf6;
                --hdr-panel-bg: rgba(20, 20, 30, 0.95);
                --hdr-text-light: #f1f5f9;
                --hdr-text-dark: #e2e8f0;
                --hdr-border-color: rgba(255, 255, 255, 0.15);
            }
            #${UI_IDS.button} {
                position: fixed; top: 50%; left: -25px; z-index: 2147483646;
                background: linear-gradient(135deg, var(--hdr-blue), var(--hdr-purple));
                color: #fff; border: none; padding: 12px 10px; border-radius: 0 12px 12px 0;
                cursor: pointer; font-size: 16px; font-weight: 600; opacity: 0.7;
                transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                backdrop-filter: blur(16px); transform: translateY(-50%);
                width: 35px; height: 50px; display: flex; align-items: center; justify-content: center;
                box-shadow: 0 8px 32px rgba(59, 130, 246, 0.3);
                border: 1px solid rgba(255, 255, 255, 0.2);
                will-change: transform, opacity, left;
            }
            #${UI_IDS.button}:hover { left: 0px; opacity: 1; transform: translateY(-50%) scale(1.1); }
            #${UI_IDS.panel} {
                position: fixed; top: 50%; left: -350px; z-index: 2147483645;
                background: var(--hdr-panel-bg); color: var(--hdr-text-light);
                border: 1px solid var(--hdr-border-color); border-radius: 0 16px 16px 0;
                padding: 0; display: none; flex-direction: column;
                font-family: 'Segoe UI', system-ui, -apple-system, sans-serif; font-size: 13px;
                width: 330px; max-height: 90vh; overflow: hidden;
                box-shadow: 0 20px 60px rgba(0,0,0,0.7); backdrop-filter: blur(20px);
                transform: translateY(-50%);
                transition: left 0.4s cubic-bezier(0.4, 0, 0.2, 1), opacity 0.4s ease;
                will-change: transform, opacity, left;
            }
            #${UI_IDS.panel}.show { left: 0px; opacity: 1; }
            .autohdr-header { padding: 20px 24px; border-bottom: 1px solid var(--hdr-border-color); position: sticky; top: 0; z-index: 10; }
            .autohdr-title { font-weight: 800; font-size: 18px; text-align: center; color: #fff; background: linear-gradient(135deg, var(--hdr-blue), var(--hdr-purple)); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text; }
            .autohdr-content { padding: 20px 24px; overflow-y: auto; flex: 1; }
            .autohdr-content::-webkit-scrollbar { width: 6px; }
            .autohdr-content::-webkit-scrollbar-thumb { background: rgba(255, 255, 255, 0.2); border-radius: 3px; }
            .autohdr-control-group { display: flex; flex-direction: column; gap: 12px; margin-bottom: 8px; padding: 16px; background: rgba(255, 255, 255, 0.03); border-radius: 12px; border: 1px solid rgba(255, 255, 255, 0.08); }
            #${UI_IDS.panel} label { display: flex; justify-content: space-between; align-items: center; font-weight: 500; font-size: 12px; color: var(--hdr-text-dark); }
            #${UI_IDS.panel} input[type="number"], #${UI_IDS.panel} input[type="text"], #${UI_IDS.panel} textarea {
                width: 70px; background: rgba(255, 255, 255, 0.08); color: var(--hdr-text-light);
                border: 1px solid rgba(255, 255, 255, 0.2); padding: 8px 12px; border-radius: 8px;
                transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1); font-size: 12px;
            }
            #${UI_IDS.panel} textarea { width: 100%; min-height: 50px; }
            #${UI_IDS.panel} input:focus, #${UI_IDS.panel} textarea:focus { outline: none; border-color: rgba(59, 130, 246, 0.6); background: rgba(255, 255, 255, 0.12); }
            #${UI_IDS.panel} input[type="checkbox"] { margin-right: 10px; transform: scale(1.2); accent-color: var(--hdr-blue); cursor: pointer; }
            #${UI_IDS.saveButton} {
                padding: 16px 24px; background: linear-gradient(135deg, var(--hdr-blue), var(--hdr-purple));
                color: #fff; border: none; border-radius: 12px; cursor: pointer; font-weight: 700;
                font-size: 13px; transition: all 0.3s ease; margin-top: 20px; width: 100%;
                text-transform: uppercase; letter-spacing: 1px;
            }
            #${UI_IDS.saveButton}:hover { transform: translateY(-3px); box-shadow: 0 12px 35px rgba(59, 130, 246, 0.5); }
            .autohdr-section-title { font-weight: 700; margin: 20px 0 12px 0; color: var(--hdr-blue); font-size: 13px; text-transform: uppercase; letter-spacing: 1.5px; padding-bottom: 8px; border-bottom: 1px solid rgba(59, 130, 246, 0.2); }
            .autohdr-presets-container { display: grid; grid-template-columns: repeat(2, 1fr); gap: 12px; }
            .autohdr-preset-btn { background: rgba(255,255,255,0.05); border: 1px solid rgba(255,255,255,0.1); border-radius: 12px; color: var(--hdr-text-light); cursor: pointer; padding: 16px 12px; font-size: 12px; transition: all 0.3s ease; text-align: center; }
            .autohdr-preset-btn:hover { background: rgba(255,255,255,0.1); border-color: var(--hdr-blue); transform: translateY(-4px); }
            .autohdr-preset-btn.clicked { animation: clickPulse 0.6s ease; }
            @keyframes clickPulse { 50% { transform: scale(1.05); } }
            .preset-icon { font-size: 22px; display: block; margin-bottom: 4px; }
            .preset-name { font-weight: 700; text-transform: uppercase; }
            .preset-desc { font-size: 10px; color: var(--hdr-text-dark); }
            .autohdr-notification {
                position: fixed; top: 24px; right: 24px; z-index: 2147483647;
                background: linear-gradient(135deg, #22c55e, #16a34a);
                color: #fff; padding: 16px 24px; border-radius: 12px; font-size: 13px; font-weight: 600;
                box-shadow: 0 12px 40px rgba(34, 197, 94, 0.4);
                transform: translateX(120%); transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
            }
            .autohdr-notification.show { transform: translateX(0); }
            .autohdr-notification.error { background: linear-gradient(135deg, #ef4444, #dc2626); }
            .hdr-enhanced-video { image-rendering: auto !important; }
        `);
    }

    // -----------------------------------------------------------------------------
    // SECTION: INITIALIZATION
    // -----------------------------------------------------------------------------

    function init() {
        loadSettings();

        if (settings.enableGUISettings) {
            if (document.body) {
                createSettingsGUI();
            } else {
                document.addEventListener('DOMContentLoaded', createSettingsGUI, { once: true });
            }
        }

        startMutationObserver();

        window.addEventListener('storage', (event) => {
            if (event.key === SCRIPT_NAME) {
                loadSettings();
                startMutationObserver();
            }
        });

        window.addEventListener('autoHDRSettingsChanged', () => {
            // Settings are already updated in the 'settings' variable
            startMutationObserver();
        });

        if (document.readyState === 'complete') {
            debouncedProcessMedia();
        } else {
            window.addEventListener('load', debouncedProcessMedia, { once: true });
        }
    }

    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', init, { once: true });
    } else {
        init();
    }

})();
